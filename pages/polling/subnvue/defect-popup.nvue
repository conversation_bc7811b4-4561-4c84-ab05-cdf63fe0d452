<!-- 巡检缺陷弹框 -->
<template>
  <view class="popup">
    <view class="u-flex u-row-center popup-head u-row-center">
      <view class="u-flex-6 u-flex u-row-center u-flex-wrap u-m-r-10">
        <!-- subnvue中不支持修改button的背景颜色 -->
        <view
          class="popup-button popup-button-primary u-flex u-row-center"
          :class="{ 'popup-button-fixed-width': isAndroid, 'u-p-l-60': !isAndroid, 'u-p-r-60': !isAndroid }"
          @tap="toRegister('normal', currentContent)"
          v-if="currentContent.normalCount"
        >
          <u-image
            src="/static/polling-card/edit.png"
            class="u-flex popup-button-icon"
          />
          <text class="popup-button-text">编辑结果</text>
        </view>
        <view
          class="popup-button popup-button-primary u-flex u-row-center"
          :class="{ 'popup-button-fixed-width': isAndroid, 'u-p-l-60': !isAndroid, 'u-p-r-60': !isAndroid }"
          @tap="toRegister('normal')"
          v-else
        >
          <u-image
            src="/static/polling-card/plus.png"
            class="u-flex popup-button-icon"
          />
          <text class="popup-button-text">录入结果</text>
        </view>
      </view>
      <view class="u-flex-6 u-flex u-row-center">
        <view
          class="popup-button popup-button-warning u-flex u-row-center"
          :class="{ 'popup-button-fixed-width': isAndroid, 'u-p-l-60': !isAndroid, 'u-p-r-60': !isAndroid }"
          @tap="toRegister('defect')"
        >
          <u-image
            src="/static/polling-card/plus.png"
            class="u-flex popup-button-icon"
          />
          <text class="popup-button-text">新增缺陷</text>
        </view>
      </view>
      <!-- <view class="u-flex-4 u-flex u-row-center">
         <view
            class="popup-button popup-button-warning u-flex u-row-center"
            :class="{ 'popup-button-fixed-width': isAndroid }"
            @tap="openVoice"
          >
            <u-image
              src="/static/polling-card/voice.png"
              class="u-flex popup-button-icon"
            />
            <text class="popup-button-text">语音缺陷</text>
          </view>
       </view> -->
    </view>
    <scroll-view
      scroll-y
      scroll-with-animation
      :style="{ height: `${4 * 167}rpx` }"
      v-if="defects.length > 0 && !open"
    >
      <view
        v-for="(item, index) in defects"
        :key="item.defectId"
        class="u-flex u-row-between list-item"
        :class="{ 'not-last': index < 4 || index < defects.length - 1 }"
      >
        <view class="u-flex-col u-row-around">
          <view class="list-item-title u-flex">
            <text
              class="list-item-title-text"
              :style="{ width: `${popupItemTextWidth}px` }"
            >{{ item.name }}</text>
          </view>
          <view class="u-flex">
            <u-image
              src="/static/polling-card/clock.png"
              style="width: 28rpx;height: 28rpx;margin-right: 10rpx;"
            />
            <text class="list-item-subtitle" v-if="item.updateStatus === 1">首次</text>
            <text class="list-item-subtitle">发现时间：{{ item.createTime }}</text>
          </view>
        </view>
        <view class="u-shrink-0" style="width: 132rpx;">
          <view
            class="button button-warning"
            @tap="toRegister('defect', item)"
            v-if="item.updateStatus === 1"
          >
            <text class="button-text button-warning-text">复查</text>
          </view>
          <view
            class="button button-primary"
            @tap="toRegister('defect', item)"
            v-else
          >
            <text class="button-text button-primary-text">编辑</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
  if (uni.getSystemInfoSync().platform == "android") {

  }
  export default {
    data() {
      return {
        // 缺陷项文字宽度
        popupItemTextWidth: 0,
        isAndroid: uni.getSystemInfoSync().platform == "android"
      };
    },
    computed: {
      ...mapState('polling', ['currentObject', 'currentContent', 'defects']),
      ...mapGetters('polling', ['objectId', 'contentId'])
    },
    methods: {
      ...mapMutations('checkObject', ['SET_REGISTER_INFO']),
      ...mapMutations('polling', ['SET_DEFECT_POPUP_FLAG', 'SET_VOICE_POPUP_FLAG']),
      ...mapActions('checkObject', ['findInspectNormalById']),
      // 打开语音识别弹窗
      openVoice() {
        uni.$emit('openVoicePopup');
      },
      /**
       * 跳转登记
       * @param type normal: 录入结果, defect: 缺陷登记
       * @param item type='normal'时表示结果id，type='defect'时表示缺陷信息，传入空表示新增
       */
      async toRegister(type, item) {
        let info = {
          inspectContentId: this.contentId,
          inspectObjectId: this.objectId,
          kindId: this.currentObject.kindId,
          capitalId: this.currentObject.capitalId,
          equipmentId: this.currentObject.equipmentId,
          guid: this.currentObject.deviceId,
          objectName: this.currentObject.objectName,
          inspectContentDVOS: this.currentObject.inspectContentDVOS,
          title: ''
        };
        if (type === 'normal') {
          if (item) {
            info = {
              ...info,
              normalIds: item.normalIds,
              normalCount: item.normalCount,
              title: '编辑结果'
            };
          } else {
            info.title = '录入结果';
          }
        } else if (type === 'defect') {
          if (item) {
            const { defectSnapshotId, defectId, updateStatus, defectUuid } = item;
            info = {
              ...info,
              defectSnapshotId,
              defectId,
              defectUuid,
              title: updateStatus === 1 ? '复查缺陷' : '编辑缺陷'
            };
          } else {
            info.title = '缺陷登记';
          }
        }
        this.SET_REGISTER_INFO(info);
        uni.navigateTo({
          url: `/pages/${type}Register/${type}Register`
        });
      }
    },
    mounted() {
      const { windowWidth } = uni.getSystemInfoSync();
      // 减去两边padding和按钮宽度
      this.popupItemTextWidth = windowWidth - uni.upx2px(24) * 2 - uni.upx2px(132);
    }
  }
</script>

<style lang="scss">
  @import '@/components/inspect/inspect-object-timeline/style';
</style>
