<template>
  <view class="login-layout">
    <!-- view class="u-flex" style="width: 80%;margin: 0 auto;">
      <u-image width="50" height="50" src="/static/login/logo.png"></u-image>
      <text style="color: #fff;margin-left: 20rpx;">渝西水资源配置工程｜智慧运维管理平台</text>
    </view> -->
    <view class="position-absolute color-white text-weight-500 u-p-l-60 login-title">
      <view class="u-font-48 u-font-weight line-height-66 u-m-b-20">欢迎登录</view>
      <view class="u-font-48 u-font-weight line-height-66">智慧运维管理平台</view>
    </view>
    <view class="position-absolute color-white content-title full-vw">
      <view class="half-width u-text-center u-font-28">账号登录</view>
    </view>
    <view class="content">
      <u-form :model="form" ref="loginForm" label-width="182rpx">
        <u-form-item class="u-m-b-64" label="账号" prop="username" left-icon="" :label-style="labelStyle" :left-icon-style="iconStyle">
          <u-input v-model="form.username" placeholder="账号" />
        </u-form-item>
        <u-form-item class="u-m-b-36" label="密码" prop="password" left-icon="" :label-style="labelStyle" :left-icon-style="iconStyle">
          <u-input v-model="form.password" type="password" placeholder="请输入用户密码" password-icon />
        </u-form-item>
        <u-form-item class="password-item" label="">
          <u-checkbox-group>
            <u-checkbox v-model="remember" name="记住密码">
             <text class="u-font-24 text-gray9 u-m-l-16">记住密码</text>
            </u-checkbox>
          </u-checkbox-group>
        </u-form-item>
      </u-form>
      <u-button type="primary" size="medium" class="submit-btn" :disabled="loading" @click="submit">
        <u-loading size="28" mode="circle" v-show="loading" style="margin-right: 12rpx;" />
        <text class="u-font-32">登录</text>
      </u-button>
      <!-- <u-button type="primary" size="medium" class="submit-btn" @click="clear">
        清除
      </u-button> -->
    </view>
  </view>
</template>

<script>
  import md5 from 'js-md5';
  import { encrypt_SM4 } from  '@/utils/sm4.js';
  import { mapState, mapMutations, mapActions } from 'vuex';
  import { login, getAccount } from '@/du-server/api';
  import { getSingleFile } from '@/du-server/upload.js';
  import { downLoadImg } from '@/utils/utils.js';
  import cacheData from '@/utils/cacheData';
  import { getUserTask } from '@/du-server/system.js';
  import dayjs from "dayjs";
  export default {
    data() {
      return {
        remember: false,
        loading: false,
        iconStyle: {
          'color': '#034da2'
        },
        labelStyle: {
          'padding-left': '64rpx',
          'font-size': '28rpx',
          'color': '#303031',
          'font-weight': 400
        },
        form: {
          username: '',
          password: ''
        },
        // dg_admin
        // 85170259Hlt__
        rules: {
          username: [{
            required: true,
            message: '请输入账号',
            trigger: ['blur', 'change']
          }],
          password: [{
            required: true,
            message: '请输入密码',
            trigger: ['blur', 'change']
          }]
        }
      }
    },
    onReady() {
      const account = uni.getStorageSync('loginAccount');
      if (account && account.username && account.password) {
        this.form.username = account.username;
        this.form.password = account.password;
        this.remember = true;
      }
      this.$refs.loginForm.setRules(this.rules);
      // 登录过的用户在离线状态下重新打开应用时允许跳过登录
      if (!this.hasNetwork && uni.getStorageSync('account') !== '') {
        uni.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    },
    onShow() {
      uni.getStorageSync('inToTask', false)
    },
    computed: {
      ...mapState(['hasNetwork', 'mainBarList', 'projectId'])
    },
    methods: {
      ...mapActions('task', [
        'findPageInspectTask'
      ]),
      ...mapMutations('system', ['SET_CURRENT_ROW', 'setCurrentTab', 'setCurrent', 'setSwiperCurrent']),
      ...mapActions(['getEnums', 'getUserRole', 'getPortals']),
      ...mapMutations(['SET_PROJECT_ID', 'SET_ORGID_ID', 'SET_MAIN_BAR_LIST']),
      // 待开始的巡检任务
      getUnStartTask() {
        return new Promise((resolve, reject) => {
          const params = {
            projectId: this.projectId,
            year: dayjs().year(),
            page: 0,
            // size: 10,
            taskStatus: ['0'].join(','),
            createTimeSort: 1,
            isApp: 1
          };
          this.findPageInspectTask({
            ...params,
            size: 1000
          }).then(({ result }) => {
            this.unStartInspectTask = result.records;
            this.unStartInspectTaskTotal = result.total;
            resolve(result.total)
          });
        })
      },

      // clear() {
      //   uni.clearStorageSync();
      // },
      // 获取安全监测token
      async getMonitorToken() {
        let formData = new FormData();
        formData.append('appKey', 'a6c98823b028494fa247f97ea0f1b914');
        formData.append('appSecret', '3ad275fd7facbd4201cbddc1331fbeff');
        // const res = await getMonitorToken(formData)
        // uni.uploadFile()
        uni.uploadFile({
            url: 'https://open.ys7.com/api/lapp/token/get',
            method: 'post', // 参数类型
            formData: {
              appKey: 'a6c98823b028494fa247f97ea0f1b914',
              appSecret: '3ad275fd7facbd4201cbddc1331fbeff'
            }, // 发送参数
            dataType: 'json', // 返回数据格式
            success(response) {
              const { data } =  JSON.parse(response.data);
              uni.setStorageSync('montorAccessToken', JSON.stringify({ montorAccessToken: data.accessToken }));
            },
            fail(error) {
               console.log('error', error)
            }
        })
        // axios
        //   .post("https://open.ys7.com/api/lapp/token/get", formData)
        //   .then((res) => {
        //     localStorage.setItem(
        //       "montorAccessToken",
        //       JSON.stringify({ montorAccessToken: res.data.data.accessToken })
        //     );
        //   });
      },
      submit() {
        this.$refs['loginForm'].validate(valid => {
          if (valid) {
            const params = {
              scope: 'all',
              grant_type: 'password',
              password: encrypt_SM4(this.form.password),
              // password: md5(this.form.password),
              username: this.form.username
            };
            this.loading = true;
            this.login(params)
          }
        });
      },
      login(params) {
        uni.removeStorage('token');
        login(params).then(res => {
          if (res.statusCode === 200) {
            const { username, password } = this.form;
            // this.$store.dispatch('userLogin', this.getLoginForm());
            this.remember ?
             uni.setStorageSync('loginAccount', { username, password })
             : uni.removeStorageSync('loginAccount');
            uni.setStorageSync('username',username);
            uni.setStorageSync('token', res.data.access_token);
            this.$store.commit('SET_MENU_ITEM', { name: '一级门户-华东院' });
            uni.setStorageSync('saveTask', false);
            this.getUnStartTask().then((num) => {
              getUserTask().then(res => {
                this.mainBarList[1].count = res.data.total + num;
                this.SET_MAIN_BAR_LIST(this.mainBarList);
              });
            });
            this.getEnums(); // 获取字典
            getAccount().then(({ data }) => {
              if (data.sysPerson && data.sysPerson.orgList && data.sysPerson.orgList.length) {
                this.SET_ORGID_ID(data.sysPerson.orgList[0].id);
                // TODO
                this.SET_PROJECT_ID(1000);
              }
              this.getPortals().then(res => {
                this.getUserRole({ portalId: res.data[0].id, id: data.id}); // 获取用户角色
              });
              const avatarToken = uni.getStorageSync('avatarToken');
              const avatarTokenNew = data?.sysPerson?.avatarToken;
              if (avatarToken !== avatarTokenNew) {
                getSingleFile(data?.sysPerson?.avatarToken).then(response => {
                  const url = 'data:image/png;base64,' + uni.arrayBufferToBase64(response.data);
                  downLoadImg(url);
                });
                uni.setStorageSync('avatarToken', avatarTokenNew);
              }

              uni.setStorageSync('account', data);
              this.SET_CURRENT_ROW({});
              this.setCurrentTab('待办任务');
              this.setCurrent(0);
              this.setSwiperCurrent(0);
              this.loading = false;
              uni.switchTab({
                url: '/pages/home/<USER>'
              });

              // cacheData().then(res => {
              //   if (res) {
              //     // uni.switchTab({
              //     //   url: '/pages/home/<USER>'
              //     // });
              //     this.loading = false;
              //   } else {
              //     this.loading = false;
              //   }
              // });
            }).catch(err => {
              uni.showToast({
                title: '获取用户信息失败',
                duration: 1000,
                icon: 'none'
              });
              this.loading = false;
            });
          } else {
            this.loading = false;
            console.log('login err', res)
            uni.showToast({
              title: res.message || res.data || '登录失败',
              duration: 1000,
              icon: 'none'
            });
          }
        })
        .catch(err => {
          this.loading = false;
          console.log(err, '登录失败')
          uni.showToast({
            title: '登录失败' + JSON.stringify(err),
            duration: 1000,
            icon: 'none'
          });
        });
      },
      // 登录时转换处理登录数据
      getLoginForm() {
        let form = {}
        const { username, password } = this.form;
        form.grant_type = 'password';
        form.username = username;
        if (this.loginForm.password) {
          form.password = encrypt_SM4(password)
          // form.password = md5(password)
        } else {
          form.password = password;
        }
        if (this.loginStrategy.secondFA) {
          form.token_before = '2FA'
        }
        return form
      },

    }
  }
</script>

<style lang="scss">
  .login-layout {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    // padding: 15% 0;
    background: url('/static/login/beijing.png') 50% no-repeat;
    background-size: cover;
    .login-title {
      top: 10.82%;
      font-size: 48rpx;
    }
    .content-title {
      top: 25.25%;
    }
    .content {
      // height: calc(100vh - 474rpx);
      height: 70.81%;
      width: 100%;
      position: absolute;
      margin: auto;
      top: 29.19%;
      bottom: 0;
      // left: 0;
      // right: 0;
      background: url('/static/login/account_bg.png') no-repeat;
      background-size: cover;
      // background-color: rgba($color: #FFFFFF, $alpha: 0.9);
      padding: 118rpx 30rpx;
      /deep/ .u-form-item {
        padding: 0;
      }
      /deep/ .u-form-item.u-border-bottom,
      /deep/ .u-form-item__body {
        border-width: 0;
        &::after {
          border-width: 0;
        }
      }

      .password-item {
        /deep/ .u-form-item__body {
          background: transparent;
          .u-checkbox {
            line-height: 32rpx;
          }
          .u-checkbox__icon-wrap {
            width: 32rpx !important;
            height: 32rpx !important;
            border-radius: 4rpx;
            border: 2rpx solid #DDDDDD;
          }
        }
      }
      /deep/ .u-form-item__body {
        width: 100%;
        height: 88rpx;
        background: #F8F8F9;
        border-radius: 44rpx;
      }

      /deep/ .u-form-item--right {
        padding-right: 64rpx;
      }

      .submit-btn {
        width: 100%;
        height: 88rpx;
        margin-top: 30rpx;
        border-radius: 44rpx;
        background-color: #027AFF;
        &.u-primary-hover {
          background-color: #027AFF;
        }
        &.u-btn--primary--disabled {
          background-color: #027AFF !important;
        }
      }
    }
  }
</style>
