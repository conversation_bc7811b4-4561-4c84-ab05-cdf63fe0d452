<!-- 视频弹框 -->
<template>
  <video
    id="currentVideo"
    :src="src"
    controls
    autoplay
    :style="{ width, height }"
  />
</template>

<script>
  export default {
    data() {
      return {
        src: null,
        width: '0',
        height: '0'
      };
    },
    created() {
      uni.$on('video-popup', data => {
        this.src = data.src;
        this.width = data.width;
        this.height = data.height;
      });
    },
		beforeDestroy() {
			uni.$off('video-popup');
		}
  }
</script>
