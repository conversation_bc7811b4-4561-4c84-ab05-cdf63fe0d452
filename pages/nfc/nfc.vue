<template>
  <view class="container u-flex-col u-col-center">
    <view class="u-flex" style="height: 70%;">
      <view class="icon-container u-flex u-row-center">
        <u-icon
          name="saoma"
          size="120"
          color="#fff"
          custom-prefix="custom-icon"
        />
      </view>
    </view>
    <text class="text">请将手机靠近NFC码进行定位确认</text>
    <u-modal v-model="successShow" :show-title="false" @confirm="back">
      <view class="u-flex-col modal-body">
        <u-image
          src="/static/icon-success.png"
          width="175"
          height="127"
          style="margin: 40rpx 0;align-self: center;"
        />
        <text class="modal-text">扫码定位已成功，你可以选择检查内容并登记缺陷</text>
      </view>
    </u-modal>
    <u-modal
      v-model="failShow"
      :show-title="false"
      confirm-text="手动打卡"
      :cancel-text="cancelText"
      show-cancel-button
      @confirm="locate"
      @cancel="back(true)"
    >
      <view class="u-flex-col modal-body">
        <u-image
          src="/static/icon-fail.png"
          width="175"
          height="127"
          style="margin: 40rpx 0;align-self: center;"
        />
        <text class="modal-text">{{ failMessage }}</text>
      </view>
    </u-modal>
  </view>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex';
  import judgePermission from '@/utils/judgePermission';
  import NFC from '@/utils/nfc';
  import { getDistance } from '@/utils/map';
  import permission from '@/js_sdk/wa-permission/permission.js'
  let isIos
  let isAndroid
  // #ifdef APP-PLUS
  let fUN_AmapLocation = null;
  // let nfcModule  = null;
  let KJNFC  = null;
  // let ByUniPlugin  = null;

  isIos = plus.os ? plus.os.name == "iOS" : false //uni.getSystemInfoSync().platform == "ios"
  // isAndroid = uni.getSystemInfoSync().platform == "android"
  isAndroid =  plus.os.name == "Android"
  console.log(isIos, isAndroid, plus.os.name, '----')
  if (isIos) {
    KJNFC = uni.requireNativePlugin('KJ-NFCTag');
    // nfcModule = uni.requireNativePlugin("Am-NFCReaderIDUniPlugin-TestModule")
    // ByUniPlugin = uni.requireNativePlugin('by-BYNFC-BYnfcModule')
  }
  if (isAndroid) {
    fUN_AmapLocation = uni.requireNativePlugin('FUN-AmapLocation');
  }
  // #endif
  export default {
    data() {
      return {
        nfc: null,
        successShow: false,
        failShow: false,
        failMessage: '',
        latitude: null,
        longitude: null,
        cancelText: '取消',
        isSuccess: false,
        nfcNo: '' // 读取的nfc
      };
    },
    computed: {
      ...mapState('task', ['taskId', 'guid']),
      ...mapState('polling', ['currentObject', 'lastLocation']),
      ...mapState({
        hasNetwork: state => state.hasNetwork
      })
    },
    onLoad(options) {
      this.latitude = options.lastLatitude;
      this.longitude = options.lastLongitude;
      this.SET_IS_NFC('nfcLoad');
    },
    // 监听nfc时页面组件会重复调用onHide和onShow，请用beforeDestroy和created代替
    created() {
      // #ifdef H5
      this.showFailModal('请检查设备是否支持并开启NFC功能');
      return false;
      // #endif
      // #ifdef APP-PLUS
      // uniNfc.start({
      //     success(res) {
      //         console.log('NFC服务初始化成功', res);
      //     },
      //     fail(err) {
      //         console.error('NFC服务初始化失败', err);
      //     }
      // });
      if (isIos) {
        KJNFC.isSupportsNFCRead((res) => {
          console.log("isSupportsNFCRead：" + JSON.stringify(res))
          res.result && this.beginSession();
        });
        //扫描方法
          // ByUniPlugin.StartScan({

          // },
          // (ret) => {
          //   console.log('eeee', ret)
          //     uni.showToast({
          //         title:'调用StartScan方法 ' + ret,//回调参数resultMsg、resultCode、payload、type、identifier、serialNumber
          //         icon: "none"
          //     })
          // })
        return false;
      }

      judgePermission('nfc', () => {
        judgePermission('location', () => {
          if (isAndroid) {
            this.nfc = new NFC(this.readSuccess, this.readError);
            this.nfc.readData();
          }
        }, this.showFailModal);
      }, this.showFailModal);
      // #endif
    },
    mounted() {
       // if (nfcModule && uni.getSystemInfoSync().platform == "ios") {
       //    nfcModule.testAsyncFunc({ 'name': '' }, (res) => {
       //      console.log('nfcModule===', res)
       //   })
       // }
       console.log('currentObject nfc', this.currentObject);
    },
    beforeDestroy() {
      if (this.loading){
        return false;
      }
      this.nfc && this.nfc.clear();
      this.SET_IS_NFC('nfcDestroy');
    },
    onBackPress(options) {
      if (this.loading ){
        return false;
      }
      this.nfc && this.nfc.clear();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (isAndroid) {
        fUN_AmapLocation.stop({}, result => {});
      }
      this.SET_IS_NFC('nfcLoad');
      // #endif
    },
    methods: {
      ...mapActions('polling', ['findInspectObjectList', 'saveScanCodeRecord']),
      ...mapMutations('polling', ['SET_IS_NFC', 'SET_NFC_PAGE']),
      beginSession() {
      	/**
      	 * ios11-ios12，只有读取，ios13及以上，有读与写
      	 * */
      	var dic = {
      		"alertMessage": "准备扫描，请将标签贴近手机", //提示的信息
      		"invalidateAfterFirstRead": false, //是否第一个NDEF标签读取成功后，会话将自动失效
      	}
        const _this = this;
      	KJNFC.beginSession(dic, (res) => {
      		console.log(JSON.stringify(res));
      		if (res.type == "beginSession") {
      			console.log("开始：" + JSON.stringify(res))
      		} else if (res.type == "readerSessionDidBecomeActive") { //ios13及以上才会回调
      			console.log("开始完成：" + JSON.stringify(res))
      		} else if (res.type == "didInvalidateWithError") {
      			console.log("扫描失败：" + _this.isSuccess + _this.nfcNo + JSON.stringify(res))
            !_this.isSuccess && res.error.includes('ession invalidated by user') && _this.showFailModal('用户取消扫描，继续检查可手动打卡');
            // 没有开启NFC权限
            if (res.error.includes('NFC radio is disabled')) {
              // this.showFailModal('请检查设备是否支持并开启NFC功能');
              // KJNFC.setAlertMessage({
              //     "alertMessage": "在“设置”中打开NFC，然后重试"
              // }, (res) => {
              //     console.log("setAlertMessage：" + JSON.stringify(res))
              // });
              uni.navigateBack();
            }
      			if (res.errorCode == 200) {
      				console.log("取消扫描")
      			} else if (res.errorCode == 201) {
      				console.log("扫描超时")
      			}
      		} else if (res.type == "didDetectNDEFs") { //ios11-ios12,才会回调
      			console.log("检测NDEF：" + JSON.stringify(res))
      		} else if (res.type == "didDetectTags") { //从这里开始，某个环节失败，都不会往下执行，ios13及以上才会回调
      			console.log("读取标签：" + JSON.stringify(res))
      			var tags = res.tags; //返回读取到的所有标签，isAvailable-是否可用
      			if (tags.length > 0) {
      				var tagIndex = 0; //传tags数组的索引
                _this.readSuccess({ data: res.tags[tagIndex].identifier })
                _this.nfcNo = res.tags[tagIndex].identifie;
                _this.isSuccess = true;
                // this.writeNfc();
                console.log('res.tags[tagIndex].identifier', res.tags[tagIndex].identifier)
                 KJNFC.invalidateSession((res) => {
                                    console.log("invalidateSession：" + JSON.stringify(res))
                                });
      			}
      		}
      	});
      },
      writeNfc() {
        var tagIndex = 0; //传tags数组的索引
        KJNFC.connectToTag({
          "tagIndex": tagIndex
        }, (res) => {
          console.log("连接到标签：" + JSON.stringify(res))
          if (res.result == true) {
            KJNFC.queryNDEFStatus({
                "tagIndex": tagIndex
            }, (res) => {
              console.log("查询NDEF状态：" + JSON.stringify(res))
              if (res.result == true) {
                var status = res.status; //NDEF状态 1-不允许NDEF读取和写入 2-可读写 3-只读
                if (status == 2 || status == 3) {
                  KJNFC.readNDEF({
                    "tagIndex": tagIndex
                  }, (res) => {
                    //res.records
                    console.log("读NDEF：" + JSON.stringify(
                        res))
                  });
                }
                if (status == 2) {
                  /**
                   * typeNameFormat: 类型名称 Empty NFCWellKnown Media AbsoluteURI NFCExternal Unknown Unchanged
                   * type: //类型 1代表：http://www. 2代表：https://www. 3代表：http:// 4代表：https:// 5代表：tel: 6代表：mailto: 大于6代表写入ndef内容
                   * 当类型为U是，payload可以填入-https://www.example.com  -mailto:<EMAIL>  -tel:+1851000888  -sms:+1851000888   -facetime://:<EMAIL>
                   * identifier: 唯一id,可以随便设置
                   * payload: 内容
                   * */
                  var dic = {
                    "tagIndex": tagIndex,
                    "records": [{
                        "typeNameFormat": "NFCWellKnown",
                        "type": "U",
                        "identifier": "123456",
                        "payload": "https://www.example.com"
                    }]
                  }
                  KJNFC.writeNDEF(dic, (res) => {
                    console.log("写NDEF：" + JSON.stringify(
                        res))
                  });
                }
              }
            })
          }
        })
      },
      back(flag = false) {
        if (this.cancelText === '开启NFC设置' && flag) {
          // #ifdef APP-PLUS
          // if (isAndroid) {
          //   const Intent = plus.android.importClass("android.content.Intent");
          //   const mIntent = new Intent('android.settings.NFC_SETTINGS'); // 打开NFC设置页面
          //   const mainActivity = plus.android.runtimeMainActivity();
          //   mainActivity.startActivity(mIntent);
          // }
          permission.gotoNfcPermissionSetting();
          // #endif
          this.failShow = false;
        }
        if (this.cancelText === '开启定位设置' && flag) {
          permission.gotoLocationPermissionSetting();
          this.failShow = false;
        }
        this.SET_NFC_PAGE(true);
        uni.navigateBack();
      },
      showFailModal(message) {
        // this.failShow = true;
        this.failShow = !!message;
        this.failMessage = message || '请检查设备是否支持并开启定位功能';
        this.cancelText = this.failMessage === '请检查设备是否支持并开启NFC功能' ? '开启NFC设置': this.failMessage === '请检查设备是否支持并开启定位功能' ? '开启定位设置' : '取消';
      },
      readSuccess({ data }) {
        //  C3359535
        console.log('readSuccess data currentObject',data, this.currentObject)
        // console.log('readSuccess data',data, this.currentObject.nfcNo)
        if (data && data === this.currentObject.nfcNo) {
          this.saveRecord(
            { scanType: 1, nfcNo: data },
            res => {
              if (res.success) {
                this.findInspectObjectList(this.taskId);
                this.successShow = true;
              }
            }
          );
        } else {
          this.showFailModal('NFC标签已损坏或NFC码与该巡检对象不匹配，继续检查可手动打卡');
        }
      },
      readError(msg = { type: 'read', errorType: 'noNFC', message: '扫码定位失败，请检查设备是否正常，继续检查可手动打卡'}) {
        console.log('扫码定位失败 data', this.currentObject)
        this.showFailModal(msg?.message || '扫码定位失败，请检查设备是否正常，继续检查可手动打卡');
        // this.showFailModal('扫码定位失败，请检查设备是否正常，继续检查可手动打卡');
      },
      /**
       * 手动打卡
       */
      locate() {
        this.saveRecord({ scanType: 3, nfcNo: null }, res => {
          if (res.success) {
            uni.showToast({
              title: '打卡成功',
              icon: 'none'
            });
            this.findInspectObjectList(this.taskId);
            setTimeout(this.back, 1000);
          } else {
            uni.showToast({
              title: '打卡失败',
              icon: 'none'
            });
            setTimeout(this.back, 2000);
          }
        })
      },
      /**
       * 打卡
       * @param {scanType, nfcNo} params
       * scanType 0: 无, 1: nfc, 2: 拍照, 3: 手动打卡
       * nfcNo nfc id
       * @param callback 保存的回调
       */
      saveRecord(params, callback = () => {}) {
        const _this = this;
        if (!this.hasNetwork) {
          _this.latitude = null;
          _this.longitude = null;
          _this.saveScanCode(params, callback);
          return false;
        }
        _this.saveScanCode(params, callback);
        // uni.getLocation({
        //   type: 'gcj02',
        //   // type: 'wgs84',
        //   success({ latitude, longitude }) {
        //     // #ifdef APP-PLUS
        //     if (isAndroid) {
        //       _this.amapStart(params, callback);
        //     }
        //     if (isIos) {
        //       _this.latitude = latitude;
        //       _this.longitude = longitude;
        //       _this.saveScanCode(params, callback);
        //     }
        //     // #endif
        //     // #ifdef H5
        //     _this.latitude = latitude;
        //     _this.longitude = longitude;
        //     _this.saveScanCode(params, callback);
        //     // #endif
        //   },
        //   fail() {
        //     _this.latitude = null;
        //     _this.longitude = null;
        //     _this.saveScanCode(params, callback);
        //   }
        // });
      },
      saveScanCode(params, callback) {
        const _this = this;
        _this.saveScanCodeRecord({
            inspectTaskId: _this.taskId,
            inspectGuid: _this.guid,
            objectId: _this.currentObject.objectId,
            fileToken: null,
            codeX: _this.longitude,
            codeY: _this.latitude,
            ...params
          })
            .then(res => {
              callback.call(_this, res);
            });
      },
      // 精准定位
      amapStart(params, callback) {
        const _this = this;
         uni.showLoading({
           title: '打卡中...'
         });
        fUN_AmapLocation.once({
          intervalTime: 1000,
          locationMode: 1,
          purpose: 3,
          gpsFirst: true,
          locationCacheEnable: false
        }, result => {
          uni.hideLoading();
          if (result.code === 0) {
            _this.latitude = result.latitude;
            _this.longitude = result.longitude;
          } else {
            _this.latitude = null;
            _this.longitude = null;
          }
          _this.saveScanCode(params, callback);
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100vw;
    height: 100vh;
    background: linear-gradient(225deg, #44C5FF 0%, #396EEE 100%);
  }

  .icon-container {
    background: url("/static/bg-icon.png") no-repeat;
    background-size: 516rpx 516rpx;
    width: 516rpx;
    height: 516rpx;
  }

  .text {
    font-size: 36rpx;
    color: #fff;
  }

  .modal-body {
    padding: 60rpx 40rpx;
  }

  .modal-text {
    color: #4A4A4A;
    font-size: 36rpx;
    font-weight: 500;
  }
</style>
