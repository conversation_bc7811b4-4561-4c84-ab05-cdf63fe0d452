<template>
  <view>
    <nav-bar page-title="随手拍" />
    <view class="locationCell">
      <u-icon class="uIcon" name="map-fill" />
      <u-input v-model="address" :disabled="disabled" class="u-p-l-20"></u-input>
    </view>
    <view class="describeView">
      <view class="input-body">
        <u-field
          v-if="isShowTextarea"
          v-model="content"
          label="描述内容"
          placeholder="请填写描述内容"
          type="textarea"
          required
          placeholder-style="color:#A9A9A9;font-size:32rpx"
          :maxlength="200"
        />
        <view class="inputLength">
          {{content ? content.length : 0}}/200
        </view>
      </view>
      <view class="clearfix">
        <my-record ref="myRecord" />
      </view>
    </view>
    <view class="uploadView">
      <view class="picTxtView">
        <text>*</text>
        上传照片
        <!-- 上传照片(最多6张) -->
      </view>
      <view class="uploadBox">
        <photograph ref="photograph" :is-task="true"/>
      </view>
    </view>
    <u-gap height="102" />
    <submit-view @submit="submit" :submitLoading="submitLoading" />
  </view>
</template>

<script>
import {mapState, mapActions, mapMutations} from 'vuex';
  // import { findDistanceByCoordinate } from '@/du-server/api';
  import dayjs from 'dayjs';
  import navBar from '@/components/inspect/nav-bar/nav-bar.vue';
  import myRecord from '@/components/inspect/my-record/my-record.vue';
  import photograph from '@/components/inspect/photograph/photograph.vue';
  import submitView from '@/components/inspect/submit-view/submit-view.vue';
  import { getDistance, getAddress } from '@/utils/map';
  let isIos
  let isAndroid
  // #ifdef APP-PLUS
  isIos = plus.os.name == "iOS" //uni.getSystemInfoSync().platform == "ios"
  isAndroid = uni.getSystemInfoSync().platform == "android"
  const fUN_AmapLocation = isAndroid ? uni.requireNativePlugin('FUN-AmapLocation') : null;
  // #endif

  const innerAudioContext = uni.createInnerAudioContext();

  innerAudioContext.autoplay = true;
  export default {
    components: {
      navBar,
      myRecord,
      photograph,
      submitView
    },
    data() {
      return {
        disabled: false,
        submitLoading: false,
        locating: true,
        latitude: null,
        longitude: null,
        address: '',
        content: '',
        isShowTextarea: true,
        fileList: []
      };
    },
    computed: {
      ...mapState({
        taskId: state => state.task.taskId,
        mileage: state => state.task.mileage,
        hasNetwork: state => state.hasNetwork
      }),
      ...mapState('polling', [
        'lastLocation'
      ])
    },
    onLoad(options) {
      this.latitude = options.lastLatitude;
      this.longitude = options.lastLongitude;
      this.locating = false; // 定位提示
      const _this = this;
      getAddress({longitude: options.lastLongitude, latitude: options.lastLatitude}).then(res1 => {
        _this.address = res1.address;
        console.log('address', res1.address)
        _this.SET_LOCATION_INFO({
          latitude: _this.latitude,
          longitude: _this.longitude,
          address: _this.address
        });
      });
      this.watchKeyWord()
    },
    mounted() {
      // this.getAddress();
    },
    onUnload() {
      // #ifdef APP-PLUS
      isAndroid ? fUN_AmapLocation.stop({}, result => {}) : '';
      // #endif
    },
    methods: {
      ...mapActions('checkObject', ['saveInspectPhoto']),
      ...mapMutations('polling', ['SET_LOCATION_INFO']),
      // 监听键盘高度变化
      watchKeyWord(){
        uni.onKeyboardHeightChange(res => {
          if(res.height == 0){
          this.isShowTextarea = false
          setTimeout(()=>{
            this.isShowTextarea = true
          },20)
          }
        })
      },
      getAddress() {
        this.disabled = true;
        this.address = '正在定位...';
        const _this = this;
        uni.getLocation({
          type: 'gcj02',
          // type: 'wgs84',
          geocode: true,
          success(res) {
            // #ifdef APP-PLUS
            const {longitude, latitude} = res;
            isAndroid && _this.amapStart({longitude, latitude});
            if (isAndroid) {
              return false;
            }
            _this.disabled = false;
            _this.latitude = res.latitude;
            _this.longitude = res.longitude;
            getAddress({longitude, latitude}).then(res1 => {
              _this.address = res1.address;
              if (res.address && !_this.address) {
                const {
                  province,
                  city,
                  district,
                  street,
                  poiName,
                  streetNum
                } = res.address;
                _this.address = province + city + district + street + poiName;
              }
            });
             _this.locating = false;
            // #endif
            // #ifdef H5
            _this.disabled = false;
            _this.latitude = res.latitude;
            _this.longitude = res.longitude;
            if (res.address) {
              const {
                province,
                city,
                district,
                street,
                poiName,
                streetNum
              } = res.address;
              _this.address = province + city + district + street + poiName + streetNum;
            } else {
              const {longitude, latitude} = res;
              getAddress({longitude, latitude}).then(res => {
                _this.address = res.address;
              });
            }
            _this.locating = false;
             // #endif
          },
          fail() {
            _this.disabled = false;
            _this.latitude = null;
            _this.longitude = null;
            _this.address = '定位失败';
            _this.locating = false;
          }
        });
      },
      async submit() {
        if (this.locating) {
          uni.showToast({
            title: '正在定位，请稍候...',
            duration: 1000,
            icon: 'none'
          });
          return;
        }
        const voices = this.$refs['myRecord'].getMsgList();
        const photos = this.$refs['photograph'].getImgList();
        if (
        this.content === '' || this.content.trim() === ''
          // (this.content === '' || this.content.trim() === '') &&
          // voices.length === 0
        ) {
          uni.showToast({
            // title: '请填写描述内容或按住说话',
            title: '请填写描述内容',
            duration: 1000,
            icon: 'none'
          });
          return;
        }
        if (photos.length === 0) {
          uni.showToast({
            title: '请上传照片',
            duration: 1000,
            icon: 'none'
          });
          return;
        }
        this.submitLoading = true;
        // if (this.hasNetwork && this.longitude && this.latitude && this.taskId) {
        //   await findDistanceByCoordinate({
        //     distance: this.mileage,
        //     latitude: this.latitude,
        //     longitude: this.longitude,
        //     taskId: this.taskId,
        //     createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        //   });
        // }
        const param = {
          createTime: dayjs(this.checkTime).format('YYYY-MM-DD HH:mm:ss'),
          photoId: String(new Date().getTime()),
          inspectTaskId: this.taskId,
          address: this.address,
          content: this.content,
          voices,
          photos
        };
        console.info('🚀🚀', '随手拍文件上传接口参数 -->', param, `<-- instaShot.vue/submit`)
        this.saveInspectPhoto(param).then(res => {
          if (res.success) {
            uni.showToast({
              title: '保存成功',
              duration: 1000,
              icon: 'success'
            });
            setTimeout(() => {
              uni.navigateTo({
                url: `/pages/instaShotList/instaShotList?lastLatitude=${this.latitude}&lastLongitude=${this.longitude}`
              });
            }, 1000);
          }
        });
      },
      // 精准定位
      amapStart(location, count = 0) {
        const _this = this;
        fUN_AmapLocation.once({
          intervalTime: 1000,
          locationMode: 1,
          purpose: 3,
          gpsFirst: true,
          locationCacheEnable: false
        }, result => {
          console.log('随手拍定位---', result)
          if (result.code === 0) {
            _this.disabled = false;
            _this.latitude = result.latitude;
            _this.longitude = result.longitude;
            if (result.address) {
              _this.address = result.address;
            } else if (result) {
              const {
                province,
                city,
                district,
                street,
                poiName,
                streetNum
              } = result;
              _this.address = province + city + district + street + poiName + streetNum;
            } else if (result.longitude && result.latitude) {
              // #ifdef APP-PLUS
              if (this.hasNetwork) {
                getAddress({longitude: location.longitude, latitude: location.latitude}).then(res => {
                  _this.address = res.address;
                });
              } else {
                _this.address = '-';
              }
              // #endif
            } else {
              count < 5 ? _this.amapStart(location, count++) : ''
              _this.address = '-';
            }
            _this.locating = false;
        } else {
          _this.latitude = null;
          _this.longitude = null;
          _this.address = '-';
          _this.locating = false;
        }
      }, (error) => {
        console.log(error)
      })
    }

    },
  }
</script>

<style lang="scss" scoped>
  /deep/ .u-input__input {
    font-size: 38rpx;
  }

  .locationCell {
    background: #ffffff;
    width: 100vw;
    display: flex;
    align-items: center;
    padding: 24rpx 35rpx;
    font-size: 36rpx;
    margin-bottom: 21rpx;

    .uIcon {
      color: #1890FF;
    }

    uni-text {
      margin-left: 16rpx;
      color: #4A4A4A;
    }
  }

  .describeView {
    background: #ffffff;
    margin-bottom: 21rpx;

    .input-body {
      position: relative;

      /deep/ .u-border-bottom {
        padding-left: 40rpx;
      }

      .inputLength {
        font-size: 32rpx;
        color: #A9A9A9;
        line-height: 45rpx;
        position: absolute;
        bottom: 8rpx;
        right: 35rpx;
        z-index: 10;
      }
    }

    .u-field {
      height: 260rpx;
    }

    /deep/ .u-label-text {
      font-size: 32rpx;
    }
  }

  .speakView {
    display: flex;
    justify-content: flex-end;
    padding-right: 35rpx;
  }

  .uploadView {
    background: #ffffff;
    padding: 24rpx 35rpx 0 35rpx;

    .picTxtView {
      font-size: 34rpx;

      uni-text {
        color: rgb(251, 53, 52);
      }
    }

    .uploadBox {
      padding: 24rpx 0;
    }
  }
</style>
