<template>
  <layout page-title="通讯录" name="addressBook" :is-back="false" style="min-height: 100vh;">
    <template slot="page">
      <!--  #ifdef  APP-PLUS -->
      <easy-search
        style="width: 100%; z-index: 999;"
        :style="{position:isAndroid? 'fixed' :'relative', top: isIos ? '18upx' : '208upx', 'margin-top': isAndroid && isSamsung ? '-20upx' : 0}"
        @onClear="onClear"
        @onSearch="onSearch"
        placeholder="请输入姓名/电话号码"
      ></easy-search>
      <!-- #endif -->
      <!--  #ifdef  H5 -->
      <easy-search
        style="position: fixed;width: 100%; z-index: 999;top:136upx;"
        @onClear="onClear"
        @onSearch="onSearch"
        placeholder="请输入姓名/电话号码"
      ></easy-search>
      <!-- #endif -->
      <view class="after-search" :style="{'margin-top': isAndroid ? '120upx' : '110upx'}">
        <view style="height: auto">
        <!--  <u-cell-group v-if="!isSearch" title="企业黄页"></u-cell-group> -->
          <tree-root
            v-if="!isSearch"
            :list.sync="tree"
            :checkbox="false"
            name="name"
            children="children"
          ></tree-root>
         <!-- <u-cell-group v-if="!isSearch" title="项目黄页"></u-cell-group>
          <tree-root
            v-if="!isSearch"
            :datalist="tree1"
            :checkbox="false"
            name="name"
            children="children"
          ></tree-root> -->
          <tree-root
            v-if="isSearch"
            :nameData="nameData"
            :searchlist="result"
            :checkbox="false"
            type="search"
            :loadData="false"
          ></tree-root>
        </view>
      </view>
      <transition name="slide-down">
        <router-view class="pop-transition-view"></router-view>
      </transition>
    </template>
  </layout>
</template>

<script>
  import TreeRoot from '@/components/tree/treeRoot';
  import { getFormsearch } from '@/du-server/system.js';
  import { getUsers } from '@/du-server/user.js';
  import { mapState, mapMutations, mapActions } from 'vuex';
  import mixin from '@/utils/minPage.js';
  export default {
    name: 'addressBook',
    mixins: [mixin],
    components: {
      TreeRoot
    },
    computed:{
      tree () {
        console.log('openAccordion', this.$store.state.deptTree)
        return this.$store.state.deptTree;
      }
    },
    data() {
      return {
        isSearch: false,
        nameData: [],
        result: [],
        tree1: [],
        isLoad: false,
        isSamsung: false,
        isAndroid: uni.getSystemInfoSync().platform == "android",
        isIos: uni.getSystemInfoSync().platform == "ios"
      }
    },
    onLoad () {
      this.refreshList();
    },
    onShow() {
      // #ifdef APP-PLUS
      const systemInfo = uni.getSystemInfoSync();
      this.isSamsung = systemInfo.brand === 'samsung';
      // #endif
      if (this.isLoad) {
        this.onLoadPage();
      }
      this.isLoad = true
    },
    onBackPress() {
      return true;
    },
    methods: {
      ...mapActions(['getDept']),
      onLoadPage() {
        this.refreshList();
      },
      refreshList() {
        this.getDept( {
          id: 0,
          wait: false
        });
      },
      // 清除
      onClear () {
        this.isSearch = false
      },
      // 搜索
      async onSearch (val) {
        if (!val.trim()) {
          this.isSearch = false
          return false
        }
        for (let i in this.tree) {
          this.prjSearchId = this.tree[i].content.id
        }
        try {
          const params = {
            // orgNo: "",
            // relationId: this.prjSearchId,
            searchValue: val,
            pageSize: 25,
            pageNo: 1,
            accountStatus: 1
          }
          uni.showLoading({
            title: '加载中'
          });
          const res = await getUsers(params);
          const { data } = res;
          this.nameData = data.list;
          this.result = data.list;
          this.isSearch = true;
          uni.hideLoading();
        } catch(e) {
          uni.hideLoading();
          return e;
        }
      },
      // 查询
      async getFormsearch() {
        try {
          const params = {
            entityName: "Project",
            like: "",
            order: "",
            page: 1,
            searchParamList: ["prjName", "prjCode", "prjManageUserName", "prjSimpleName", "userFullname"],
            size: 20
          }
          const res = await getFormsearch(params);
          const { data } = res;
          // this.tree1 = data.list
          // for (let i = 0; i < this.tree1.length; i++) {
          //   this.tree1[i].flag = 1
          //   this.tree1[i].children = []
          // }
        }catch(e) {
          return e;
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
.after-search {
  position: relative;
  // top: 110upx;
  // top: calc(var(--status-bar-height) + var(--u-nav-height)) ;
  // height: calc(100vh - var(--status-bar-height) - var(--u-nav-height));
  min-height: calc(100vh - 240upx - var(--status-bar-height) - var(--u-nav-height))
}
.transition-view {
  background-color: #efeff4;
}
.phoneBook {
  font-size: 18px;
  font-weight: 400;
}

.moa-search-div {
  width: 100%;
  position: relative;
  top: 45px;
  padding: 0.6rem 0.8rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  background-color: #ffffff;
  z-index: 100;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.02),
    0px 0px 4px 0px rgba(0, 0, 0, 0.02);
  font-size: 1rem;
}

</style>
