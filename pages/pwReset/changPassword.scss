.send-button {
  font-size: 0.87rem;
  color: #fff;
  padding: 0 0.4rem;
  border-radius: 4px;
  width: auto;
  text-align: center;
  background: #347df6;
  &:active {
    background: #a9a9a9;
  }
  &.disabled {
    width: 8rem;
    background: transparent;
    padding: 0.4rem 0;
    color: #a9a9a9;
    &:active {
      background: transparent;
      color: #777777;
    }
  }
}
.flex-center {
  align-items: center;
}

.flex-grow {
  flex: 1;
}
.flex {
  display: flex;
  display: -webkit-flex;
}
.icon-style {
  flex: 1;
  font-size: 20px;
  height: 20px;
  width: 20px;
  // margin-right: 20px;
  color: #6666;
}

.vux-u-input /deep/ .weui-icon-clear {
  display: none;
}
.weui-switch:checked {
  border-color: #1890ff;
  background-color: #1890ff;
}
.pw-box {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}
.pw-style {
  flex: 2.5;
}
.input-style {
  flex: 6;
  margin-right: 16px;
}
.clear-style {
  height: 16px;
  width: 16px;
  margin-right: 14px;
  margin-top: 2px;
}
.right-style {
  // margin-right: 14px;
}
/deep/ .weui-input {
  margin-top: 12px;
}
/deep/ .weui-cell:before {
  border-top: none;
}

.pw-view {
  margin-bottom: 2rem
}

.pwd-tip {
  display: block;
  color: red;
  opacity: 0.8;
}

.con-btn {
  border-radius: 100px
}