<template>
  <view style="height: 100%">
    <x-header
      :left-options="{ backText: '' }"
      style="position: fixed; width: 100%"
      >手机验证</x-header
    >
    <view
      class="main-container"
      style="position: absolute; width: 100%; top: 50px"
    >
      <view style="margin-bottom: 2rem">
        <group>
          <u-input placeholder="输入手机号码" v-model="mobile" :max="13">
            <icons slot="label" class="icon-style" style name="mphone" color="#666"></icons>
          </u-input>
          <u-input placeholder="短信验证码" v-model="code" :max="13">
            <icons slot="label" class="icon-style" style name="sms-pwc" color="#666"></icons>
            <view class="flex-grow" style="font-size: 1rem">
              {{ countDown > 0 ? "已发送验证码10分钟内有效" : "" }}
            </view>
            <view
              class="send-button resend"
              @click="resetCount"
              slot="right"
              :class="{ disabled: countDown > 0 }"
            >
              <text>{{
                firstFlag
                  ? "发送验证码"
                  : countDown > 0
                  ? countDown + "秒后可重新发送"
                  : "重新发送"
              }}</text>
            </view>
          </u-input>
          <!-- <cell title="姓名" :value="userRealName"></cell> -->

          <!-- <number-keyboard
              :show="showKeyboard"
              @input="onInput"
              @delete="onDelete"
              @blur="showKeyboard = false"
              close-button-text="完成"
              :safe-area-inset-bottom="true"
          />-->
          <!-- <verification-code-input v-model="code" :amount="4"></verification-code-input> -->
        </group>
        <u-input
          v-if="pwType"
          placeholder="输入新的登录密码"
          type="password"
          v-model="password"
          @blur.native.capture="passwordCherked()"
        >
          <icons slot="label" class="icon-style" style name="lock" color="#666"></icons>
          <inline-x-switch
            slot="right"
            @on-change="pwTypeChange()"
            v-model="pwType"
          ></inline-x-switch>
        </u-input>
        <u-input
          v-else
          placeholder="输入新的登录密码"
          v-model="password"
          @blur.native.capture="passwordCherked()"
        >
          <icons slot="label" class="icon-style" style name="lock" color="#666"></icons>
          <inline-x-switch
            slot="right"
            @on-change="pwTypeChange()"
            v-model="pwType"
          ></inline-x-switch>
        </u-input>
      </view>

      <default-button
        style="border-radius: 100px"
        :disabled="!pwCheck || !password || !code || !showKeyboard"
        @click="goNext"
        >完成</default-button
      >
    </view>
  </view>
</template>

<script>
// import { PasswordInput, NumberKeyboard } from "vant"
import { XInput, InlineXSwitch } from "vux"
import defaultButton from "@/components/defaultButton"
import md5 from "js-md5"
import { getUrl } from "@/assets/js/utils.js"
export default {
  data () {
    return {
      value: "",
      showKeyboard: false,
      code: [],
      firstFlag: true,
      countDown: 0,
      mobile: "",
      userName: "",
      userRealName: "",
      password: "",
      pwType: true,
      code: "",
      key: "",
      pwCheck: false,

      pwRule: {
        // length: 0,
        // characterNumber: 0,
        // lowercaseCount: 0,
        // upercaseCount: 0,
        // composeCount: 0
      }
    }
  },
  mounted () {
    this.userName = this.$route.query.userName
    this.userRealName = this.$route.query.userRealName
    this.getPwRule()
  },
  components: {
    defaultButton,
    // PasswordInput,
    // NumberKeyboard,
    XInput,
    InlineXSwitch
  },
  methods: {
    getVerifyCode (callback) {
      // let param = {
      //   phone: this.mobile
      // };
      this.$ajax({
        method: "get",
        url: "sys-user/user/sms_captcha?" + getUrl({ phone: this.mobile })
        // data: {
        //    phone: this.$route.query.mobile,
        //   //phone: "18851403632"
        // }
        // params: param
      }).then((res) => {
        console.log(res)
        if (res.data.status == true) {
          callback()
          this.showKeyboard = true
          this.key = res.data.data
        } else {
          this.$vux.toast.text(res.data.message)
        }
      })
    },
    goNext () {
      const data = {
        captcha: this.code,
        key: this.key,
        phone: this.mobile,
        pwd: md5(this.password)
      }
      this.$ajax({
        method: "put",
        url: "sys-user/user/retrieve/pwd?" + getUrl(data),
        data: data,
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      }).then((res) => {
        // this.$router.push('/faceMatch/?userName=' + this.userName + '&nextRoute=pwChange') // 测试用
        // return // 测试用
        if (res.data.status == true) {
          this.$vux.toast.text("密码修改成功")
          this.$router.push("/")
        } else {
          this.$vux.toast.text(res.data.message)
        }
      })
    },
    getPwRule () {
      this.$ajax({
        pass: true,
        method: "GET",
        url: "sys-system/clientInfo?" + getUrl()
      }).then((res) => {
        // this.$router.push('/faceMatch/?userName=' + this.userName + '&nextRoute=pwChange') // 测试用
        // return // 测试用
        if (res.data.status == true) {
          const data = res.data.data
          const passwordStrategy = JSON.parse(data.passwordStrategy)
          this.pwRule.length = passwordStrategy.pwMinLength
          this.pwRule = passwordStrategy
          if (passwordStrategy.passwordLevel === "normal") {
            this.pwRule["characterNumber"] = 2
          }
        } else {
          this.$vux.toast.text("密码权限获取失败")
        }
      })
    },
    resetCount () {
      const _this = this
      console.log(Number(this.mobile))
      if (!this.mobile) {
        this.$vux.toast.text("请输入手机号")
        return
      }
      if (
        (this.countDown == 0 || this.firstFlag) &&
        !isNaN(Number(this.mobile))
      ) {
        this.getVerifyCode(() => {
          _this.countDown = 60
          _this.count()
        })
      } else {
        this.$vux.toast.text("手机号未注册")
      }
    },
    count () {
      this.firstFlag = false
      let counter = window.setInterval(() => {
        this.countDown--
        if (this.countDown == 0) {
          window.clearInterval(counter)
        }
      }, 1000)
    },
    onInput (key) {
      this.value = (this.value + key).slice(0, 4)
    },
    onDelete () {
      this.value = this.value.slice(0, this.value.length - 1)
    },
    passwordCherked () {
      if (
        this.password.replace(/[^a-zA-Z]/g, "").length <
          this.pwRule.characterNumber ||
        this.password.length < this.pwRule.pwMinLength ||
        this.pwRule.pwMaxLength < this.password.length
      ) {
        this.pwCheck = false
        this.$vux.toast.text("密码格式不符合要求")
      } else if (
        this.password.replace(/[^A-Z]/g, "").length <
          this.pwRule.upercaseCount ||
        this.password.replace(/[^a-z]/g, "").length < this.pwRule.lowercaseCount
      ) {
        this.pwCheck = false
        this.$vux.toast.text("密码格式不符合要求")
      } else {
        this.pwCheck = true
      }
    },
    watch: {
      password (newPw, oldPw) {}
    }
  }
}
</script>
 <style lang="less" scoped>
.send-button {
  font-size: 0.87rem;
  color: #fff;
  padding: 0 0.4rem;
  border-radius: 4px;
  width: auto;
  text-align: center;
  background: #347df6;
  &:active {
    background: #a9a9a9;
  }
  &.disabled {
    width: 8rem;
    background: transparent;
    padding: 0.4rem 0;
    color: #a9a9a9;
    &:active {
      background: transparent;
      color: #777777;
    }
  }
}
.flex-center {
  align-items: center;
}

.flex-grow {
  flex: 1;
}
.flex {
  display: flex;
  display: -webkit-flex;
}
.icon-style {
  font-size: 20px;
  height: 20px;
  width: 20px;
  margin-right: 20px;
  color: #666;
}

.vux-u-input /deep/ .weui-icon-clear {
  display: none;
}
.weui-switch:checked {
  border-color: #1890ff;
  background-color: #1890ff;
}
</style>
