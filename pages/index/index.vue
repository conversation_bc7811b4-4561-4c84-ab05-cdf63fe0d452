<template>
	<view class="shareImage">
		<view style="">
			<web-view id="mapContainer" :src="srcHandler()"></web-view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				bool: false,
				lat:'',
				lng:''
			}
		},
		created() {
			this.getLngLat();
		},
		methods: {
			srcHandler(){
				return `/hybrid/html/map.html?lat=${this.lat}`
			},
			getLngLat() {
				uni.getLocation({
					type: 'wgs84',
					success:res => {
						if (res.latitude) {
							this.lat = res.latitude + ',' + res.longitude;
							// this.lng = res.longitude;
						} else {
							if (uni.getSystemInfoSync().platform == 'android') {
								var context = plus.android.importClass("android.content.Context");
								var locationManager = plus.android.importClass(
									"android.location.LocationManager");
								var main = plus.android.runtimeMainActivity();
								var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
								this.bool = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)
							}
							if (this.bool === false) {
								uni.showModal({
									title: '提示',
									content: '请打开定位服务',
									success: ({
										confirm,
										cancel
									}) => {
										if (confirm) {
											if (uni.getSystemInfoSync().platform == 'android') {
												var Intent = plus.android.importClass(
													'android.content.Intent');
												var Settings = plus.android.importClass(
													'android.provider.Settings');
												var intent = new Intent(Settings
													.ACTION_LOCATION_SOURCE_SETTINGS);
												var main = plus.android.runtimeMainActivity();
												main.startActivity(intent); // 打开系统设置GPS服务页面
											}
										}
									}
								});
							}
						}
					}
				});
			}
		}
	}
</script>
