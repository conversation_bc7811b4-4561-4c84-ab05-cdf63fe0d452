<template>
  <view>
    <nav-bar title="现场检查详情" />
    <view class="card">
      <text class="title" v-if="checkInfo.name">{{ checkInfo.name }}</text>
      <text class="detail" v-if="checkInfo.allDetails">{{ checkInfo.allDetails }}</text>
      <text class="detail u-padding-top-10" v-if="checkInfo.participantName">巡检参与人：{{ checkInfo.participantName | nameFilter }}</text>
    </view>
    <view class="card" style="margin-top: 25rpx;">
      <view class="u-flex">
        <view class="u-flex-5 u-text-left">
          <text class="date-dark">开始时间</text>
          <text class="card-num">{{ checkInfo.inspectStartTime | timeFilter }}</text>
          <text class="date">{{ checkInfo.inspectStartTime | dateFilter }}</text>
        </view>
        <view class="u-flex-2 u-flex u-row-center">
          <u-image
            src="/static/xiangyou.png"
            style="width:40rpx;height: 40rpx;"
          />
        </view>
        <view class="u-flex-5">
          <text class="date-dark u-text-right">结束时间</text>
          <text class="card-num u-text-right">{{ checkInfo.inspectEndTime | timeFilter }}</text>
          <text class="date u-text-right">{{ checkInfo.inspectEndTime | dateFilter }}</text>
        </view>
      </view>
      <view
        class="u-line"
        :style="{
          borderBottom: '1px solid #E4E4E4',
          margin: '25rpx 0'
        }"
      />
      <view class="u-flex">
        <view class="u-flex-3">
          <text class="u-text-center date">里程</text>
          <view class="u-flex u-row-center">
            <text class="content">{{ checkInfo.mileage || '-' }}</text>
            <text class="content" v-if="checkInfo.mileage">km</text>
          </view>
        </view>
        <view class="u-flex-6">
          <text class="u-text-center date">检查耗时</text>
          <text class="u-text-center content">{{ checkInfo.inspectTimeConsuming || '-' }}</text>
        </view>
        <view class="u-flex-3">
          <text class="u-text-center date">暂停时长</text>
          <text class="u-text-center content">{{ checkInfo.pauseTime || '-' }}</text>
        </view>
      </view>
    </view>
    <view class="u-flex subtitle">
      <u-image
        src="/static/polling-card/location.png"
        style="width: 50rpx;height: 50rpx;margin-right: 10rpx;"
      />
      <text class="subtitle-text">检查轨迹</text>
    </view>
    <my-map
      height="292rpx"
      :preview="true"
      :coordinates="coordinates"
      @click="fullscreenMap"
    />
    <view class="u-flex subtitle">
      <u-image
        src="/static/polling-card/xiangji.png"
        style="width: 50rpx;height: 50rpx;margin-right: 10rpx;"
      />
      <text class="subtitle-text">随手拍</text>
    </view>
    <view
      v-if="checkInfo.inspectPhotoList && checkInfo.inspectPhotoList.length"
    >
      <view
        class="card insta-card"
        v-for="(item, i) in checkInfo.inspectPhotoList"
        :key="i"
        :style="{ marginTop: i > 0 ? '24rpx' : 0 }"
      >
        <text class="u-line-1 insta-card-title">{{ item.content }}</text>
        <view class="u-flex u-flex-wrap u-m-b-18" style="margin-top: 18rpx;">
          <u-image
            src="/static/icon-position.png"
            style="width: 28rpx;height: 28rpx;margin-right: 6rpx;"
          />
          <!-- 地址无法换行，暂时这样解决 -->
          <text
            class="insta-card-text"
            v-for="char in (item.address || '-').split('')"
            :key="char"
          >{{ char }}</text>
        </view>
        <view
          class="u-flex u-flex-wrap card-body"
          v-if="item.photos && item.photos.length"
        >
          <view
            :style="{
              width: `${instaWidth}px`,
              height: `${instaWidth}px`,
              margin: '6rpx',
              'background-color': '#EBEBEB'
            }"
            v-for="(val, i) in item.photos"
            :key="i"
          >
            <u-image
              :style="{
                width: `${instaWidth}px`,
                height: `${instaWidth}px`
              }"
              :src="val.url"
              mode="aspectFill"
              :border-radius="10"
              v-if="val.extName === 'pic'"
              @tap="viewMedia(val.url)"
            />
            <view
              :style="{
                width: `${instaWidth}px`,
                height: `${instaWidth}px`
              }"
              class="fullContent warpCenter bg-video"
              @tap="showVideo(val.url)"
              v-else-if="val.extName === 'video'"
            >
              <u-image
                src="/static/bofang.png"
                style="width: 90rpx;height: 90rpx;"
                v-if="val.url"
              />
              <u-image
                src="/static/jinzhibofang.png"
                style="width: 90rpx;height: 90rpx;"
                v-else
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    <no-data v-else />
    <view class="u-flex subtitle">
      <u-image
        src="/static/polling-card/wenti.png"
        style="width: 50rpx;height: 50rpx;margin-right: 10rpx;"
      />
      <text class="subtitle-text">缺陷列表</text>
    </view>
    <view
      v-if="checkInfo.taskDefectSnapshotDVOS && checkInfo.taskDefectSnapshotDVOS.length"
    >
      <view
        class="card defect-card"
        v-for="(item, i) in checkInfo.taskDefectSnapshotDVOS"
        :key="item.defectId"
        :style="{ marginTop: i > 0 ? '24rpx' : 0 }"
      >
        <text class="u-line-1 defect-card-title">{{ item.name }}</text>
        <view class="u-flex defect-card-body">
          <text class="tag tag-cyan" v-if="item.objectTypeName">{{ item.objectTypeName }}</text>
          <text class="tag tag-blue" v-if="item.equipmentTypeName">{{ item.equipmentTypeName }}</text>
        </view>
        <view class="u-flex defect-card-footer">
          <u-image
            src="/static/icon-time.png"
            style="width: 28rpx;height: 28rpx;margin-right: 10rpx;"
          />
          <text class="defect-card-footer-text">首次发现时间：{{ item.createTime }}</text>
        </view>
      </view>
    </view>
    <no-data v-else />
  </view>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import spliceUrl from '@/utils/spliceUrl';
  import navBar from '@/components/inspect/nav-bar/nav-bar';
  import myMap from '@/components/inspect/my-map/my-map';
  export default {
    components: {
      navBar,
      myMap
    },
    data() {
      return {
        windowWidth: 0,
        windowHeight: 0,
        // 随手拍图片/视频宽度
        instaWidth: 0,
        coordinates: [],
        checkInfo: {}
      }
    },
    computed: {
      ...mapState('task', ['taskId']),
      ...mapState(['hasNetwork']),
    },
    mounted() {
      this.initWidth();
      this.initFonts();
      uni.showLoading({
        mask: true,
        title: '加载中'
      });
      this.findCoordinateInfoByTaskId(this.taskId)
        .then(res => {
          this.coordinates = res.success && Array.isArray(res.result)
            ? res.result
            : [];
        });
      this.findInspectTaskDetails(this.taskId).then(res => {
        uni.hideLoading();
        if (res.success) {
          this.checkInfo = res.result;
          if (this.hasNetwork) {
            this.checkInfo.inspectPhotoList = this.checkInfo.inspectPhotoList.map(photo => {
              photo.photos = spliceUrl(photo.photos);
              return photo;
            });
          }
        } else {
          uni.showToast({
            title: '查询失败',
            icon: 'none'
          })
        }
      });
    },
    beforeDestroy() {
      const subNVue = uni.getSubNVueById('videoPopup');
      subNVue.show('fade-out', 250);
    },
    methods: {
      ...mapActions('checkObject', [
        'findInspectTaskDetails'
      ]),
      ...mapActions('task', [
        'findCoordinateInfoByTaskId'
      ]),
      initWidth() {
        const { windowWidth, windowHeight } = uni.getSystemInfoSync();
        this.windowWidth = windowWidth;
        this.windowHeight = windowHeight;
        // 减去两边padding，再减掉单个项的两边margin
        this.instaWidth = (windowWidth - uni.upx2px(35) * 2) / 4 - uni.upx2px(6) * 2;
      },
      initFonts() {
        // nvue引入字体文件
        const domModule = weex.requireModule('dom');
        domModule.addRule('fontFace', {
          fontFamily: 'led',
          src: `url('/static/fonts/led.ttf')`
        });
      },
      showVideo(url) {
        if (url) {
          const width = `${this.windowWidth}px`;
          const height = `${this.windowHeight / 2}px`;
          uni.$emit('video-popup', { src: url, width, height });
          const subNVue = uni.getSubNVueById('videoPopup');
          subNVue.setStyle({
            width,
            height
          });
          subNVue.show('fade-in', 250);
        }
      },
      viewMedia(url) {
        // 获取图片信息
        uni.previewImage({
          urls: [url]
        });
      },
      fullscreenMap() {
        uni.navigateTo({
          url: `/pages/map/map?inspectTaskId=${this.taskId}`
        });
      },
      // 补0
      num(n) {
        return n && n < 10 ? '0' + n : n;
      }
    },
    filters: {
      nameFilter(names) {
        return names.replace(/,/g, '、');
      },
      timeFilter(time) {
        if (time && time.length >= 19) {
          return time.substring(11, 19)
        }
      },
      dateFilter(val) {
        if (val && val.length >= 10) {
          let date = val.substring(5, 10).split('-');
          let month = Number(date[0]);
          let day = Number(date[1]);
          return month + '月' + day + '日';
        }
      }
    }
  };
</script>

<style lang="scss">
  @import "@/components/inspect/photograph/style.scss";

  .card {
    background-color: #fff;
    padding: 25rpx 33rpx;
  }
  .insta-card-title {
    color: #272727;
    font-size: 30rpx;
    font-weight: bold;
  }
  .insta-card-body {
    margin-top: 20rpx;
  }
  .insta-card-text {
    color: #4A4A4A;
    font-size: 24rpx;
  }
  .insta-card {
  }
  .defect-card-title {
    color: #000000;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 50rpx;
  }
  .defect-card-body {
    margin-top: 33rpx;
  }
  .defect-card-footer {
    margin-top: 20rpx;
  }
  .defect-card-footer-text {
    color: #6E6E6E;
    font-size: 28rpx;
  }
  .title {
    margin-bottom: 16rpx;
    border-left: 8rpx solid #2F80FF;
    padding-left: 34rpx;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 36rpx;
  }

  .detail {
    margin-left: 42rpx;
    font-size: 30rpx;
    font-weight: bold;
    margin-top: 20rpx;
  }

  .card-num {
    font-family: led;
    color: $u-type-primary;
    font-size: 65rpx;
    line-height: 100rpx;
  }

  .date-dark {
    color: #4A4A4A;
    font-size: 30rpx;
    font-weight: bold;
  }

  .date {
    color: #6E6E6E;
    font-size: 30rpx;
  }

  .content {
    padding-top: 10rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .subtitle {
    padding: 20rpx 25rpx;
  }
  .subtitle-text {
    font-size: 32rpx;
    font-weight: bold;
  }
  .tag {
    height: auto;
    margin-right: 24rpx;
    border-radius: 6rpx;
    padding: 4rpx 16rpx;
    background-color: #ECEEFD;
    font-size: 26rpx;
    font-weight: bold;
  }

  .tag-cyan {
    color: #43B4DC;
  }
  .tag-blue {
    color: #2F80FF;
  }
</style>
