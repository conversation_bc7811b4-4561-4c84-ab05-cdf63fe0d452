<template>
  <view>
    <nav-bar title="缺陷" />
    <template v-if="list.length > 0">
      <view
        class="card"
        v-for="item in list"
        :key="item.defectSnapshotId"
        @click="onClick(item.defectId)"
      >
        <view class="u-line-1 card-title">
          {{ item.name }}
        </view>
        <u-gap height="33" />
        <view class="card-body">
          <u-tag
            class="tagCom card-body-tag"
            bg-color="#ECEEFD"
            color="#43B4DC"
            v-if="item.objectTypeName"
            :text="item.objectTypeName"
          />
          <u-tag
            class="tagCom card-body-tag"
            bg-color="#ECEEFD"
            color="#2F80FF"
            v-if="item.equipmentTypeName"
            :text="item.equipmentTypeName"
          />
        </view>
        <u-gap height="25" />
        <view class="card-footer">
          <view class="card-footer-left">
            <u-icon
              name="shijian1"
              color="#d0d0d0"
              custom-prefix="custom-icon"
              size="28"
              label-color="$u-type-info"
            />
            <text class="card-footer-left-label">首次发现时间:</text>
            <text class="card-footer-left-data">{{ item.createTime || '--' }}</text>
          </view>
        </view>
      </view>
    </template>
    <u-loadmore :status="loadStatus" :style="{ padding: '10rpx 0' }" />
  </view>
</template>

<script>
  import { mapActions, mapState } from 'vuex';
  import navBar from '@/components/inspect/nav-bar/nav-bar.vue';

  export default {
    components: {
      navBar
    },
    data() {
      return {
        inspectTaskId: '',
        inspectGuid: '',
        list: [],
        loadStatus: 'loading'
      };
    },
    onLoad(option) {
      this.inspectTaskId = option.inspectTaskId;
      this.inspectGuid = option.inspectGuid;
    },
    onShow() {
      this.findByAllDefectByTaskId({taskId: this.inspectTaskId, guid: this.inspectGuid})
        .then(res => {
          if (res.success) {
            this.list = res.result || [];
          }
        })
        .finally(() => {
          this.loadStatus = 'nomore';
        });
    },
    computed: {
      ...mapState(['hasNetwork']),
    },
    methods: {
      ...mapActions('task', [
        'findByAllDefectByTaskId'
      ]),
      onClick(id) {
        if (id && this.hasNetwork) {
          uni.navigateTo({
            url: `/pages/problemDetail/problemDetail?defectId=${id}`
          });
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  $footer-color: #6E6E6E;

  .card {
    background: #ffffff;
    margin: 20rpx 0;
    padding: 31rpx;

    .card-title {
      font-size: 36rpx;
      color: #000000;
      line-height: 50rpx;
    }

    .card-body-subTitle {
      font-size: 32rpx;
      color: #272727;
      line-height: 45rpx;
    }

    .card-body-tag {
      margin: 0 12rpx;
    }

    .card-footer {
      border-top: 1px dashed $u-bg-color;
      padding-top: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-footer-left-label {
        color: $footer-color;
        margin-left: 8rpx;
      }

      .card-footer-left-data {
        color: $footer-color;
        margin-left: 16rpx;
      }

      .card-footer-right {
        color: $footer-color;
        font-size: 24rpx;
        line-height: 33rpx;
      }
    }
  }
</style>
