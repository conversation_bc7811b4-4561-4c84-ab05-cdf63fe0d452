<template>
  <layout page-title="附件预览" name="previewFile" style="height: 100%" :is-nav="false"></layout>
</template>
<script>
import { fileIcon } from '@/utils/utils.js';
import { resGet } from '@/du-server/app.js';
import { config } from '@/config/index.js'
let lastRouter = ''
// 文件预览
export default {
  name: 'PreView',
  data () {
    return {
      percent: 0,
      name: "",
      fileSize: "0",
      ft: "",
      complete: true,
      downloadFlag: false,
      file: {},
      preId: "",
      options: {},
      loading: true,
      src: ""
    }
  },
  onLoad (options) {
    if (options.file) {
      // console.log(JSON.parse(decodeURIComponent(options.file)), '8')
      this.file = JSON.parse(decodeURIComponent(options.file))
    }
    this.options = options;
    this.percent = 0
    this.name = this.row.name
    this.fileSize = this.row.fileSize
    this.$store.commit('setFileRouter', lastRouter)

    if (options.preId) {
      this.$store.commit('setPreId', options.preId)
    }
    this.download()
  },
  mounted() {
    // this.download()
  },
  computed: {
    userInfo() {
      return this.$store.state.user
    },
    row() {
      return this.$store.state.downloadFile
      // return this.file
    },
    currentRow () {
      // 页面中使用row来指代当前记录
      return this.$store.state.currentRow
    }
  },
  methods: {
    backhome () {
      // if (lastRouter.indexOf('noticeInfo') != -1) {
      if (this.options.name === 'noticeInfo') {
        // uni.reLaunch({
        //   url: `/pages/newsNotice/noticeInfo?Uid=${this.options.preId}&type=${this.options.type}`
        // });
        uni.navigateBack();
      } else {
        uni.navigateBack({
          delta: 1
        })
      }
    },
    getUrl () {
      // 获取新的url链接
      // let url = process.env.TOKEN_URL + 'sys-storage/download?f8s=' + this.file.fileToken
      let url = config.baseURL + '/sys-storage/download?f8s=' + this.file.fileToken
      if (this.row.type != undefined && this.row.type != "") {
        url = this.row.server
      }
      // console.log(url, process.env.TOKEN_URL)
      return url
    },
    // 预览文件
    previewFile(url, typeName) {
      //支持预览的文件类型
      // 微信小程序
      let fileType = ['doc', 'xls', 'ppt', 'pdf', 'docx', 'xlsx', 'pptx' ];
      const _this = this;
      // if(!fileType.includes(typeName)) {
      //   return uni.showToast({
      //     title: '不支持预览当前文件类型',
            //     icon: 'none'
      //   })
      // }
      uni.showLoading({
        title: '正在打开',
        mask: true
      })
      // 下载文件资源到本地
      uni.downloadFile({
        url: url,
        success(res) {
          uni.hideLoading();
          var filePath = res.tempFilePath;
          // if(!fileType.includes(typeName)) {
          //   return false;
          // }


          uni.showLoading({
            title: '正在打开',
            mask: true
          })
          // 新开页面打开文档，支持格式：doc, xls, ppt, pdf, docx, xlsx, pptx。
          uni.openDocument({
            filePath: filePath,
            // filePath: escape(filePath),
            // filePath: encodeURI(filePath),
            // fileType: typeName.split('.')[1],// 文件类型，指定文件类型打开文件，有效值 doc, xls, ppt, pdf, docx, xlsx, pptx
            // fileType: 'pdf',// 文件类型，指定文件类型打开文件，有效值 doc, xls, ppt, pdf, docx, xlsx, pptx
            // showMenu: true, // 允许出现分享功能
            success: res => {
              uni.hideLoading();
              _this.backhome();
            },
            fail: openError => {
              console.log('fail openError:' + JSON.stringify(openError));
              console.log('fail openError-----' , typeName.split('.')[1], filePath);
              uni.showToast({
                title: '暂不支持此类型',
                duration: 2000
              });
              uni.hideLoading();
            }
          });
        },
        fail: function(err) {
          uni.hideLoading();
          console.log('fail:' + JSON.stringify(err));
        }
      });
    },
    // 预览图片
    previewImg(imgUrl) {
      const _this = this;
      // 预览图片
      uni.previewImage({
        urls: [ imgUrl ],
        complete(data) {
          _this.backhome();
        },
        longPressActions: {
          // itemList: ['发送给朋友', '保存图片', '收藏'],
          success(data) {
            console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
          },
          fail(err) {
            console.log(err.errMsg);
          }
        }
      });
    },
    download () {
      let url = this.getUrl();
      let filePath = this.file?.fileName;
      var index = filePath ? filePath.lastIndexOf(".") : -1;
      var ext = index !== -1 ? filePath.substr(index+1) : '';
      if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(ext.toLowerCase()) !== -1) {
        this.previewImg(url)
      } else {
        this.previewFile(url, filePath)
      }
    },
    // 返回
    goBack () {
      // 左上角返回事件
      // this.ft.abort()
      // this.$router.go(-1)
      const lastRouter = this.$store.state.fileRoute
      this.preId = this.$store.state.preId ? this.$store.state.preId : null
      console.log("this.$store.state.preId", this.$store.state.preId, lastRouter)
      // this.preId = this.$route.params.preId
      if (lastRouter == "fileinfo") {
        // this.$router.push("/fileinfo/文件库")
        uni.navigateTo({
          url: '/fileinfo/文件库'
        })
      } else if (this.options.name === 'noticeInfo') {
        uni.reLaunch({
          url: `/pages/newsNotice/noticeInfo?Uid=${this.preId}&type=${this.options.type}`
        });
      } else {
       uni.navigateTo({
         url: `${lastRouter}?Uid=${this.preId}&taskId=${this.preId}&indexId=${this.options.indexId}&unitFlag=${this.options.unitFlag}&indexFullHome=${this.options.indexFullHome}&type=${this.options.type}&nextId=${this.options.nextId}`
       })
      }
    },
    changeFileName (name) {
      const type = (name + "")
        .substring((name + "").lastIndexOf("."))
        .toLowerCase()
      const fileName = (name + "").substring(0, (name + "").lastIndexOf("."))
      switch (type) {
        case ".docx":
        case ".doc":
        case ".xlsx":
        case ".xls":
        case ".ppt":
        case ".pptx":
          return fileName + ".pdf"
        default:
          return name
      }
    },
    fileIcon,
  },
  beforeRouteEnter (to, from, next) {
    console.log("path", from.path)

    lastRouter = from.name
    next()
  }
}
</script>
<style scoped>
.fileSize {
  text-align: center;
  font-size: 0.6rem;
  color: #a9a9a9;
}
.preview-file {
  padding: 2rem 0 1rem;
  width: 20%;
  height: auto;
}
/deep/ .vux-header {
  padding-top: 0 !important;
}
/deep/ .vux-header .vux-header-left .left-arrow {
  top: -5px !important;
}
</style>
