<template>
  <layout name="monitorDetail" :page-title="project.projectName" :customBack="goBack" :isDown="show">
    <template #right>
      <text
        class="cursor-pointer u-font-34"
        style="height: 42px;line-height: 42px;"
        @click.stop="toProcessLine"
      >过程线</text>
    </template>
    <template slot="page">
     <view class="monitor-container">
        <!-- 顶部搜索条件 -->
        <u-dropdown ref="uDropdown" @open="openDropdown" @close="closeDropdown" :class="{ 'shadow': show }">
          <date-range-dropdown-item
            @load="dateRangeLoad"
            @confirm="dateRangeConfirm"
           />
           <filter-dropdown-item
             :options="codeOptions"
             default-title="测点"
             @confirm="codeConfirm"
           />
          <u-dropdown-item ref="moreDropdownItem" title="高级过滤" class="bg-white">
            <scroll-view scroll-y="true" style="height: 50vh;" @touchmove.stop.prevent="()=>{}">
              <u-cell-group
                v-for="param in paramList"
                :key="param.fieldName"
                :title="param.name"
              >
                <u-cell-item
                  v-for="{ name, value } in param.options"
                  :arrow="false"
                  :key="value"
                  :title="name"
                  :title-style="isSelected(param.fieldName, value) ? {
                    color: '#1890FF'
                  } : {}"
                  size="large"
                  center
                  @click="selectChange(param.fieldName, value)"
                >
                  <template #right-icon>
                    <!-- #ifdef  H5 -->
                    <icons
                      name="success"
                      size="20px"
                      color="#1989fa"
                      v-show="isSelected(param.fieldName, value)"
                    />
                    <!-- #endif -->
                    <!--  #ifdef  APP-PLUS  -->
                    <u-icon name="checkmark"  size="40" color="#1989fa"></u-icon>
                   <!-- <image src="/static/common/icon/success.png" color="#1989fa"></image> -->
                    <!-- #endif -->
                  </template>
                </u-cell-item>
              </u-cell-group>
              <view class="u-p-16">
                <u-button
                  type="primary"
                  block
                  :customStyle="customStyle"
                  @click="moreDropdownItemConfirm"
                >确定</u-button>
              </view>
            </scroll-view>
          </u-dropdown-item>
        </u-dropdown>
        <code-value-hot-table :lazy="true" :params="hotTableParams" ref="table"/>
        <phone-orient-button ref="orientButton" />
      </view>
    </template>
  </layout>
</template>
<script>
import { cloneDeep } from 'lodash';
import moment from 'moment';
import mixin from '@/utils/minPage.js';
import dateRangeDropdownItem from '@/components/monitor/dropdown/dateRangeDropdownItem';
import filterDropdownItem from '@/components/monitor/dropdown/filterDropdownItem';
import phoneOrientButton from '@/components/monitor/button/phoneOrientButton';
import codeValueHotTable from '@/components/monitor/table/codeValueHotTable';
import { getProjectById, getCodesByProject } from '@/du-server/monitor.js';

export default {
  mixins: [mixin],
  components: {
    dateRangeDropdownItem,
    filterDropdownItem,
    phoneOrientButton,
    codeValueHotTable
  },
  data () {
    return {
      show: false,
      customStyle: {
        borderRadius: '0'
      },
      project: {},
      codeOptions: [],
      options: {},
      paramList: [
        {
          name: '采集类型',
          fieldName: 'codeAutoList',
          options: [
            { name: '人工', value: 0 },
            { name: '自动', value: 1 }
          ],
          values: [0, 1]
        },
        {
          name: '测值状态',
          fieldName: 'valueStatusList',
          options: [
            { name: '正常', value: 1 },
            { name: '异常', value: 2 },
            { name: '错误', value: 3 }
          ],
          values: [1, 2, 3]
        },
        {
          name: '审核状态',
          fieldName: 'valueCheckList',
          options: [
            { name: '未审核', value: 0 },
            { name: '通过', value: 1 },
            { name: '未通过', value: 2 }
          ],
          values: [0, 1, 2]
        }
      ],
      hotTableParams: {},
      url: '',
      // 参数用于传参
      params: {
        codeId: '',
        codeAutoList: [0, 1],
        valueStatusList: [1, 2, 3],
        valueCheckList: [0, 1, 2],
        valueTypeList: [0, 1, 2],
        startTime: '',
        endTime: ''
      }
    }
  },
  onLoad(options) {
    this.options = options;
    // this.setOrientation();
  },
  onUnload() {
    // #ifdef APP-PLUS
    plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
    plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
    // #endif
  },
  methods: {
    refreshList() {
      this.getProject();
    },
    reachBottom() {
      console.log('reachBottom')
      this.$refs.table.loadData();
    },
   setOrientation() {
     //  #ifdef APP-PLUS
     const _this = this;
     const res = uni.getSystemInfoSync();
      // 屏幕方向数值： HOME键在右, 0 - 竖屏； 90 - 横屏；  HOME'键在左, 180 - 反向竖屏； -90 - 反向横屏;
      let orientation = plus.navigator.getOrientation();
       var webView = null
      if(orientation == 0){
        // 竖屏做的操作
        setTimeout(() => {
          plus.screen.lockOrientation('portrait-primary')
        }, 10)
        // 设置web-view以外的内容
        let currentWebview = _this.$scope.$getAppWebview()
        webView = currentWebview.children()[0]
        webView.setStyle({
          top: 0,
          height: res.windowWidth,
          width: res.windowHeight
        })
      }else if(orientation == 90){
         //横屏做的操作
         setTimeout(() => {
           plus.screen.lockOrientation('landscape-primary')
         }, 10)
         // // 设置web-view以外的内容
         let currentWebview = _this.$scope.$getAppWebview()
         webView = currentWebview.children()[0]
         webView.setStyle({
           top: 80,
           height: res.windowHeight / 1.5238,
           width: res.windowHeight
         })
      }
     // #endif
   },
    // 返回
    goBack() {
      // #ifdef APP-PLUS
      plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
      plus.screen.lockOrientation('portrait-primary'); //锁死屏幕方向为竖屏
      uni.navigateTo({
        url: '/pages/monitor/monitor'
      });
      // #endif
      // #ifdef H5
      const pages = getCurrentPages();
      setTimeout(() => {
        uni.navigateBack({delta: pages.length - 1});
      }, 10);
      // #endif
    },
    // 测点确认
    codeConfirm (value) {
      this.close(); // 关闭弹窗
      this.params.codeId = value;
      this.setHotTableParams();
    },
    dateRangeLoad ({ startDate, endDate }) {
      this.params.startTime = moment(startDate).format('YYYY-MM-DD [00:00:00]')
      this.params.endTime = moment(endDate).format('YYYY-MM-DD [23:59:59]')
    },
    // 时间确认
    dateRangeConfirm (data) {
      this.close(); // 关闭弹窗
      this.dateRangeLoad(data);
      this.setHotTableParams();
    },
    // 关闭下拉菜单
    close() {
      this.show = false;
      this.$refs.uDropdown.close(); // 关闭弹窗
    },
    // 打开下拉菜单
    openDropdown(index) {
      // 展开某个下来菜单时，先关闭原来的其他菜单的高亮
      // 同时内部会自动给当前展开项进行高亮
      this.$refs.uDropdown.highlight();
      this.show = true;
    },
    // 关闭下拉菜单
    closeDropdown(index) {
      // 关闭的时候，给当前项加上高亮
      // 当然，您也可以通过监听dropdown-item的@change事件进行处理
      this.$refs.uDropdown.highlight(index);
      this.show = false;
    },
    /**
     * 选择筛选项
     * @param fieldName 字段名称
     * @param value 值
     */
    selectChange (fieldName, value) {
      const values = this.paramList
        .find(param => param.fieldName === fieldName)
        .values
      const index = values.indexOf(value)
      if (index > -1) {
        values.splice(index, 1)
      } else {
        values.push(value)
      }
    },
    /**
     * 当前项是否已选择
     * @param fieldName 字段名称
     * @param value 值
     */
    isSelected (fieldName, value) {
      return this.paramList
        .find(param => param.fieldName === fieldName)
        .values
        .indexOf(value) > -1
    },
    /**
     * 确认高级筛选
     */
    moreDropdownItemConfirm () {
      this.paramList.forEach(({ fieldName, values }) => {
        this.params[fieldName] = cloneDeep(values)
      })
      this.close();
      // this.$refs.moreDropdownItem.toggle(false)
      this.setHotTableParams()
    },
    // 跳转过程线页面
    toProcessLine () {
      const { id } = this.options;
      const orientationType = this.$refs.orientButton.getOrientation()
      uni.navigateTo({
        url: `/pages/monitor/detail/processLine?id=${id}&orientationType=${orientationType}`
      })
    },
    async getCodes () {
      const { data } = await getCodesByProject(this.project)
      this.codeOptions = Array.isArray(data)
        ? data.map(item => ({ name: item.codeName, value: item.codeId }))
        : []
    },
    setHotTableParams () {
      const { id, projectInstrId, projectDamId, projectSort } = this.project
      const today = moment().format('YYYY-MM-DD')
      this.hotTableParams = {
        dayStart: `${today} 00:00:00`,
        dayEnd: `${today} 23:59:59`,
        projectId: id,
        instrId: projectInstrId,
        damId: projectDamId,
        sort: projectSort,
        ...this.params
      }
    },
    async getProject () {
      const { data } = await getProjectById(this.options?.id)
      this.project = data
      await this.getCodes()
      this.setHotTableParams()
    }
  },
  mounted () {
    this.getProject()
  },
  onBackPress(e) {
    // 避免死循环
    if (e.from === 'navigateBack') {
      return false;
    }
    if (!(e && e.from === 'navigateBack')) {
      this.goBack();
    }
    return true; // 返回true表示要自定义onBackPress
  }
}
</script>

<style lang="scss" scoped>
  /deep/ .u-popup::-webkit-scrollbar {
    display: none;
  }
  /deep/ .u-dropdown__content {
    height: calc(100vh - 68px - 40px) !important;
  }
  .u-dropdown__menu.shadow {
    /deep/ .u-dropdown__menu {
       box-shadow: 10px 0px 15px 0px rgba(0, 0, 0, .2);
    }
  }
  .monitor-table-style {
    min-height: calc(100vh - 40px - 68px);
  }
</style>
