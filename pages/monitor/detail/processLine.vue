<template>
  <layout class="monitor-container" title="过程线" name="processLine" :customBack="goBack" bgColor="#f5f5f5">
    <template slot="page">
      <u-dropdown ref="uDropdown" @open="openDropdown" @close="closeDropdown" class="bg-white position-relative" style="top: 10rpx;" z-index="181" active-color="#1890ff">
        <date-range-dropdown-item
          @load="dateRangeLoad"
          @confirm="dateRangeConfirm"
        />
        <filter-dropdown-item
          :options="codeOptions"
          :required="true"
          default-title="测点"
          :default-value="codeId"
          @confirm="codeConfirm"
        />
        <filter-dropdown-item
          :options="vectorOptions"
          :required="true"
          default-title="分量"
          :default-value="vectorId"
          @confirm="vectorConfirm"
        />
      </u-dropdown>
      <view class="u-m-t-10 flex-grow-1 ofy-auto no-scrollbar">
        <view class="bg-white">
          <view class="py-8 u-p-16">
            <view class="headline line-height u-font-32 u-m-l-10 u-m-b-0">
              {{ dateRangeTitle }}过程线
            </view>
          </view>
         <process-line-chart
            v-if="echartsData && showChart"
            class="line-chart"
            ref="processLineChart"
            :height="chartHeight"
            :id.sync="options.id"
            :chart-data.sync="echartsData"
          />
        </view>
        <view class="bg-white u-m-t-10">
          <view class="py-8 u-p-16">
            <view class="headline line-height u-font-32 u-m-l-10 u-m-b-0">
              {{ dateRangeTitle }}列表
            </view>
          </view>
          <code-value-hot-table
            class="px-5 pb-5 u-p-20 u-p-b-10 hot-table position-relative"
            :is-line="true"
            height="auto"
            :is-more="false"
            :params="hotTableParams"
            @response="setEchartsData"
          />
        </view>
      </view>
      <phone-orient-button ref="orientButton" @change="orientChange" />
    </template>
  </layout>
</template>

<script>
import moment from 'moment';
import dateRangeDropdownItem from '@/components/monitor/dropdown/dateRangeDropdownItem';
import filterDropdownItem from '@/components/monitor/dropdown/filterDropdownItem';
import phoneOrientButton from '@/components/monitor/button/phoneOrientButton';
import processLineChart from '@/components/monitor/chart/processLineEcharts';
import codeValueHotTable from '@/components/monitor/table/codeValueHotTable';
import { getCodesByProject, getProjectById } from '@/du-server/monitor.js';
import { dateFormat } from '@/utils/dateFormat';

export default {
  components: {
    dateRangeDropdownItem,
    filterDropdownItem,
    phoneOrientButton,
    processLineChart,
    codeValueHotTable
  },
  data () {
    return {
      project: {},
      codeOptions: [],
      codeId: '',
      vectorOptions: [],
      vectorId: '',
      chartHeight: '186px',
      echartsData: { data: [] },
      hotTableParams: {},
      params: {
        startTime: '',
        endTime: ''
      },
      options: { id: '' },
      show: false,
      showChart: true
    }
  },
  computed: {
    dateRangeTitle () {
      const { startTime, endTime } = this.params
      if (!startTime || !endTime) {
        return ''
      }
      return `${dateFormat(startTime)} ~ ${dateFormat(endTime)}`
    }
  },
  onLoad(options) {
    this.options = options;
    // this.setOrientation();
  },
  onUnload() {
    this.setOrientation()
  },
  onBackPress(e) {
    if (!(e && e.from === 'navigateBack')) {
      this.goBack();
    }
  },
  methods: {
    // 返回
    goBack () {
      const { id } = this.options;
      const orientationType = this.$refs.orientButton.getOrientation();
      uni.navigateTo({
        url: `/pages/monitor/detail/detail?id=400000002&orientationType=${orientationType || ''}`
      });
      this.setOrientation();
    },
    setOrientation() {
      //  #ifdef APP-PLUS
      plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
      plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
      // #endif
    },
    dateRangeLoad ({ startDate, endDate }) {
      this.params.startTime = moment(startDate).format('YYYY-MM-DD [00:00:00]')
      this.params.endTime = moment(endDate).format('YYYY-MM-DD [23:59:59]')
    },
    // 时间 点击确定
    dateRangeConfirm (data) {
      this.close(); // 关闭弹窗
      this.dateRangeLoad(data)
      this.setHotTableParams()
    },
    // 关闭下拉菜单
    close() {
      this.show = false;
      this.$refs.uDropdown.close(); // 关闭弹窗
    },
    // 测点 确定
    codeConfirm (value) {
      this.close(); // 关闭弹窗
      this.codeId = value;
      this.setHotTableParams();
    },
    // 打开下拉菜单
    openDropdown(index) {
      // 展开某个下来菜单时，先关闭原来的其他菜单的高亮
      // 同时内部会自动给当前展开项进行高亮
      this.$refs.uDropdown.highlight();
      this.show = true;
    },
    // 关闭下拉菜单
    closeDropdown(index) {
      // 关闭的时候，给当前项加上高亮
      // 当然，您也可以通过监听dropdown-item的@change事件进行处理
      this.$refs.uDropdown.highlight(index);
      this.show = false;
    },
    // 分量 确定
    vectorConfirm (value) {
      this.vectorId = value
      this.setHotTableParams()
    },
    async getCodes () {
      const { data } = await getCodesByProject(this.project)
      let codeOptions = []
      let vectorOptions = []
      if (Array.isArray(data) && data.length > 0) {
        // 更新测点和分量列表
        codeOptions = data.map(item => ({ name: item.codeName, value: item.codeId }))
        this.codeId = codeOptions[0].value
        if (
          Array.isArray(data[0].instrVectorDTOS) &&
          data[0].instrVectorDTOS.length > 0
        ) {
          vectorOptions = data[0].instrVectorDTOS.map(
            item => ({
              name: item.valueVectorName,
              value: item.valueVectorId,
              type: item.valueVectorType
            })
          )
          const vectorOption = vectorOptions.find(item => item.type === 2) ||
            vectorOptions[0]
          this.vectorId = vectorOption.value
        }
      }
      this.codeOptions = codeOptions
      this.vectorOptions = vectorOptions
    },
    // 设置表格数据
    setEchartsData (data) {
      const { startTime, endTime } = this.params
      this.echartsData = { startTime, endTime, ...data }
    },
    // 表格参数
    setHotTableParams () {
      this.hotTableParams = {
        codeId: this.codeId,
        vectorId: this.vectorId,
        codeAutoList: [0, 1],
        valueStatusList: [1, 2, 3],
        valueCheckList: [0, 1, 2],
        valueTypeList: [0, 1, 2],
        ...this.params
      }
    },
    async getProject () {
      const { id } = this.options;
      const { data } = await getProjectById(id)
      this.project = data
      await this.getCodes()
      this.setHotTableParams()
    },
    orientChange (type) {
      const _this = this;
      uni.getSystemInfo({ // 获取系统信息
        success: function (res) {
          const width = res.screenWidth * (750 / res.windowWidth);
          // 屏幕方向数值： HOME键在右, 0 - 竖屏； 90 - 横屏；  HOME'键在左, 180 - 反向竖屏； -90 - 反向横屏;
         let orientation = plus.navigator.getOrientation();
         console.log(orientation, type, width)
         _this.chartHeight = orientation === 0
           ? '250px'
           : '186px'
        }
      })
    }
  },
  mounted () {
    this.getProject()
  }
}
</script>

<style lang="scss" scoped>
  .monitor-container {
    background-color: #f5f5f5;
    .monitor-table-style {
      min-height: 56vh;
    }
  }
  /deep/.hot-table {
    & > div {
      min-height: 50vh;
      overflow: visible !important;
    }
  }
</style>
