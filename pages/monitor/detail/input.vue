<template>
  <layout class="monitor-container mh-100" name="monitorInput" :page-title="project.projectName" :customBack="goBack" :page-content-style="{'min-height': 'calc(100vh - 68px)'}">
    <template #right>
      <text
        class="cursor-pointer u-font-34"
        @click.stop="onSubmit"
      >提交</text>
    </template>
    <template slot="page">
      <u-row class="u-flex flex-shrink-0 u-row-between u-col-center input-head"  style="margin-top: 20rpx;">
        <u-col :span="3" class="u-flex align-items-center justify-content-start">
          <u-button
            icon="arrow-left"
            plain
            :custom-style="{color: '#9b9b9b'}"
            size="mini"
            class="u-m-r-6"
            :disabled="isFirstDay"
            @click.stop="previousDate"
          >
            <u-icon name="arrow-left"></u-icon>
          </u-button>
          前一天
        </u-col>
        <u-col
          :span="6"
          @click="() => {
            showDatePicker
          }"
          class="u-col-center cursor-pointer u-text-center u-flex"
        >
          <image
            @click="() => {
              showDatePicker
            }"
            src="/static/common/icon/icon_calendar_blue.png"
            alt=""
            class="icon-calendar"
          />
          <!-- {{ formattedCurrentDate }} -->
          <view  @click="() => {
            showDatePicker
          }">
            <uni-datetime-picker
              ref="picker"
              v-model="formattedCurrentDate"
              class="time-content"
              type="date"
              title="请选择"
              :border="false"
              :clearIcon="false"
              :min-date="minDate"
              :max-date="maxDate"
              :close="closePicker"
              @show="() => {
            showDatePicker
          }"
              @maskClick="closePicker"
              @change="onDateConfirm"
            />
          </view>
        </u-col>
        <u-col
          :span="3"
          class="u-flex align-items-center justify-content-end"
        >
          后一天
          <u-button
            plain
            :custom-style="{color: '#9b9b9b'}"
            size="mini"
            class="u-m-l-6"
            :disabled="isLastDay"
            @click.stop="nextDate"
          >
           <u-icon name="arrow-right"></u-icon>
         </u-button>
        </u-col>
      </u-row>
      <view class="monitor-table-style no-selection-handle">
        <!--  #ifdef  H5  -->
        <hot-table
          ref="inputTable"
          :root="root"
          stretchH="all"
          class="inputTable"
          license-key="non-commercial-and-evaluation"
          :settings="dataSetting"
          v-if="dataSetting.data && dataSetting.data.length > 0 && tableShow"
        />
        <!-- #endif -->
        <!--  #ifdef  APP-PLUS  -->
        <my-table v-if="dataSetting.data && dataSetting.data.length > 0 && tableShow" ref="myTable" editor text="测点" :settings.sync="dataSetting" :oldSettings="oldDataSetting"></my-table>
        <!-- #endif -->
        <u-empty v-else mode="list"></u-empty>
      </view>
      <phone-orient-button v-if="!datePickerVisible" />
      <transition name="van-fade" v-if="!datePickerVisible">
        <view class="u-m-18" v-show="toolbarVisible">
         <view class="toolbar u-flex u-col-bottom">
            <view class="u-font-32 cursor-pointer u-text-center" @click.stop="openLastData">上期数据</view>
            <view class="u-font-32 cursor-pointer u-text-center" @click.stop="calculate">计算</view>
          </view>
        </view>
     </transition>
      <!-- 上期数据弹框 -->
      <u-popup
        v-model="lastDataVisible"
        mode="bottom"
        height="70%"
        width="100vw"
        style="height: 70%;"
      >
       <u-row
          class="u-flex u-row-between u-col-center u-font-34 flex-shrink-0 u-p-16"
          style="height: 42px"
        >
          <u-col span="6">上期数据</u-col>
          <u-col span="6">
            <view class="text-blue u-text-right" @click="lastDataVisible = false">关闭</view>
          </u-col>
        </u-row>
        <code-value-hot-table class="flex-grow-1" :params="lastDataParams" text="测点" />
      </u-popup>
    </template>
  </layout>
</template>
<script>
  // #ifdef  H5
import { HotTable } from '@handsontable-pro/vue'
import Handsontable from 'handsontable-pro'
import 'handsontable-pro/dist/handsontable.full.css'
import HANDSONTABLE_LANGUAGE from '@/plugins/constants/handsontableLang'
// #endif
// #ifdef APP-PLUS
import MyTable from '@/components/monitor/table/index.vue'
// #endif
import { mapState, mapMutations } from 'vuex';
import codeValueHotTable from '@/components/monitor/table/codeValueHotTable.vue'
import scrollMixin from '@/components/scroll/scrollMixin'
import mixin from '@/utils/minPage.js';
import phoneOrientButton from '@/components/monitor/button/phoneOrientButton'
import { dateFormat } from '@/utils/dateFormat'
import { getTextWidth } from '@/utils/text';
import cloneDeep from 'lodash.clonedeep';
import dayjs from 'dayjs';
import {
  getProjectById,
  getEntryValueData,
  calculateValueData,
  saveEntryValueData
} from '@/du-server/monitor.js'

const DAY_MILLISECONDS = 24 * 60 * 60 * 1000
const MIN_DATE = '1900/01/01'
const MAX_DATE = '2050/10/01'

export default {
  mixins: [scrollMixin, mixin],
  components: {
    // #ifdef H5
    HotTable,
    // #endif
    // #ifdef APP-PLUS
    MyTable,
    // #endif
    phoneOrientButton,
    codeValueHotTable
  },
  data () {
    const currentDate = new Date()
    return {
      root: 'input-table',
      currentDate,
      formattedCurrentDate: dateFormat(currentDate),
      datePickerVisible: false,
      date: new Date(),
      minDate: new Date(MIN_DATE),
      maxDate: new Date(MAX_DATE),
      project: {},
      data: [],
      info: [],
      dataSetting: { data: [] },
      // Array<boolean>，每行的测点是否有测值修改，提交时仅传入已修改的测点
      dataHasChangedList: [],
      lastDataParams: {},
      lastDataVisible: false, // 上期数据 弹窗显示
      calculating: false,
      toolbarVisible: true,
      option: { id : '' },
      tableShow: false,
      oldDataSetting: {}
    }
  },
  computed: {
    ...mapState('monitor', ['selectedCode']),
    ...mapState('monitor/entry', ['entryData', 'entryDefaultData', 'entryLoading', 'entryFilter']),
    isFirstDay () {
      return this.formattedCurrentDate <= MIN_DATE
    },
    isLastDay () {
      return this.formattedCurrentDate >= MAX_DATE
    }
  },
  onLoad(option) {
    this.option = option;
  },
  onShow() {
    this.getProject();
    // #ifdef H5
    window.addEventListener('scroll', this.checkToolbarVisible)
    // #endif
  },
  onUnload() {
    // #ifdef APP-PLUS
    plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
    plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
    // #endif
  },
  methods: {
    ...mapMutations('monitor', [
      'SET_SELECTED_CODE'
    ]),
    ...mapMutations('monitor/entry', [
      'SET_ENTRY_DEFAULT_DATA',
      'SET_ENTRY_DATA',
      'SET_ENTRY_LOADING'
    ]),
    refreshList() {
      this.currentDateChange();
    },
    /**
     * 获取首列标题宽度
     * @param rowHeaders 首列标题列表
     * @return {number}
     */
    getRowHeaderWidth (rowHeaders = []) {
      const rowHeaderWidth = Math.max(
        // padding + border = 5
        ...rowHeaders.map(name => getTextWidth(name, 14) + 5 * 2)
      )
      return Math.max(rowHeaderWidth, 50)
    },
    /**
     * 获取非首列标题宽度
     * @param rowHeaders 首列标题列表
     * @return {number}
     */
    getColHeaderWidth (rowHeaders = []) {
      // #ifdef APP-PLUS
      let withs = rowHeaders.map(name => {
        return getTextWidth(name, 14) + 5 * 2
      });
      if (rowHeaders.includes('日期')) {
       withs[rowHeaders.indexOf('日期')] = getTextWidth('2022-11-07 10:10:10', 14) + 5 * 2;
      }
      const rowHeaderWidth = Math.max.apply(null, withs);
      return withs ;
      // #endif
      //  #ifdef H5
      const rowHeaderWidth = Math.max(
        ...rowHeaders.map(name => getTextWidth(name, 14) + 5 * 2)
      )
      return Math.max(rowHeaderWidth, 50)
      // #endif
    },
    goBack() {
      if (this.dataHasChangedList.some(changed => changed)) {
        uni.showModal({
          title: '',
          content: '当前已有录入，是否继续返回？',
          confirmColor: '#1989fa',
          success(res) {
            if (!res.confirm) {
              return false;
            }
            this.packPage();
          }
        });
      } else {
        this.packPage();
      }
      // #ifdef APP-PLUS
      plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
      plus.screen.lockOrientation('portrait-primary'); //锁死屏幕方向为竖屏
      // #endif
    },
    async currentDateChange () {
      this.formattedCurrentDate = dateFormat(this.currentDate)
      await this.initData()
    },
    packPage() {
      // uni.navigateTo({
      //   url: '/pages/monitor/monitor'
      // });
      uni.navigateBack({ delta: 1 });
    },
    // 显示时间弹窗
    showDatePicker () {
      this.date = new Date(this.formattedCurrentDate)
      // this.datePickerVisible = !this.datePickerVisible;
      this.datePickerVisible = true;
    },
    // 关闭时间弹窗
    closePicker() {
      this.datePickerVisible = false;
    },
    // 确认选择 时间
    onDateConfirm(newval) {
      this.closePicker();
      console.log(newval, this.formattedCurrentDate, dateFormat(this.date), newval === dateFormat(this.date))
      // if (this.formattedCurrentDate === dateFormat(this.date)) {
      if (newval === dateFormat(this.date)) {
        this.datePickerVisible = false
      } else {
        this.switchDate()
          .then(() => {
            // this.currentDate = new Date(this.date)
            this.currentDate = new Date(newval)
            console.log(this.currentDate, 'this.currentDate')
            this.currentDateChange()
            this.datePickerVisible = false
          })
          .catch((e) => {
            console.log('333', e)
            return e;
          })
      }
    },
     getEntryChangeData(arr) {
      let isInProject = this.selectedCode.itemId ? this.selectedCode.itemId.indexOf('code') === -1 : false;
      // let isInProject = false;
      arr.forEach(item => {
        let findCode = this.entryDefaultData.find(itemChild => { return item.CODE_NAME === itemChild.CODE_NAME; });
        let findAccurateCode = this.entryDefaultData.find(itemChild => {
          return item.CODE_NAME === itemChild.CODE_NAME && item.WATCH_TIME === itemChild.WATCH_TIME;
        });
        item.CODE_ID = isInProject ? findCode ? findCode.CODE_ID : item.CODE_ID : item.CODE_ID;
        item.ID = isInProject ? findAccurateCode ? findAccurateCode.ID : null : (item.ID || item.id);
        item.CODE_NAME = isInProject ? findCode ? findCode.CODE_NAME : item.CODE_NAME : item.CODE_NAME;
        item.INSTR_ID = isInProject ? findCode ? findCode.INSTR_ID : item.INSTR_ID : item.INSTR_ID;
        item.WATCH_TIME = item.WATCH_TIME ? dayjs(item.WATCH_TIME.replace(/-/g, '/')).format('YYYY-MM-DD HH:mm:ss') : item.WATCH_TIME;
        if (Object.keys(item).includes('key')) {
          Reflect.deleteProperty(item, 'key');
        }
      });
      return arr;
    },
    // 提交
    onSubmit () {
      // #ifdef H5
      if (!this.allowNext() || !this.$refs.inputTable.hotInstance) {
        return false
      }
      // #endif
      // #ifdef APP-PLUS
      if (!this.allowNext() || !this.$refs.myTable) {
        return false
      }
      // #endif
      const _this = this;
      uni.showModal({
        title: '',
        content: '您确认提交吗？',
        confirmColor: '#1989fa',
        success(res) {
          if (!res.confirm) {
            return false;
          }
          // #ifdef H5
          const sourceData = _this.$refs.inputTable.hotInstance.getSourceData() // 筛选出已修改的行
            .filter((item, i) => _this.dataHasChangedList[i]);
          // #endif
          // #ifdef APP-PLUS
          // 筛选出已修改的行
           const sourceData = _this.$refs.myTable.getSourceData()
            .filter((item, i) => _this.dataHasChangedList[i]);
          // #endif

          const { id: projectId, projectDamId } = _this.project
          const userInfoJSON = uni.getStorageSync('account');
          try {
            const { id } = userInfoJSON;
            saveEntryValueData(
              id,
              {
                valueIsAuto: 0,
                valueType: 0,
                // valueList: sourceData,
                valueList: _this.getEntryChangeData(sourceData),
                codeId: null,
                groupId: null,
                projectId,
                damId: projectDamId,
                isCover: 1
              }
            )
              .then(() => {
                uni.showToast({
                  title: '保存成功'
                });
                _this.getProject()
              })
              .catch(() => {
                uni.showToast({
                  title: '保存失败',
                  icon: 'error'
                });
              })
          } catch (e) {
            console.log('提交失败', e)
            return e;
            // 非法用户信息
          }
        }
      });
    },
    // 是否允许下一步操作
    allowNext () {
      if (this.calculating) {
        uni.showToast({
          title: '正在计算中，请稍候',
          icon: 'none'
        });
        return false
      }
      return true
    },
    switchDate () {
      if (!this.allowNext()) {
        return new Promise((resolve, reject) => reject())
      }
      if (this.dataHasChangedList.some(changed => changed)) {
        return uni.showModal({
          title: '',
          content: '当前已有录入，是否继续切换？',
          confirmColor: '#1989fa',
          success(res) {
            if (!res.confirm) {
              return false;
            }
            return true;
          }
        });
      }
      return new Promise((resolve) => resolve())
    },
    previousDate () {
      this.switchDate()
        .then(() => {
          this.currentDate = new Date(this.currentDate.getTime() - DAY_MILLISECONDS)
          this.currentDateChange()
        })
        .catch(() => {})
    },
    nextDate () {
      this.switchDate()
        .then(() => {
          this.currentDate = new Date(this.currentDate.getTime() + DAY_MILLISECONDS)
          this.currentDateChange()
        })
        .catch(() => {})
    },
    /**
     * 获取上期数据
     */
    async setLastDataParams () {
      const { id, projectInstrId, projectDamId, projectSort, lastRecTime } = this.project
      if (lastRecTime) {
        // const startTime = dateFormat(lastRecTime, 'YYYY-MM-DD [00:00:00]')
        // const endTime = dateFormat(lastRecTime, 'YYYY-MM-DD [23:59:59]')
        this.lastDataParams = {
          // startTime,
          // endTime,
          // dayStart: startTime,
          // dayEnd: endTime,
          startTime: new Date().getFullYear() - 1 + '-01-01 00:00:00',
          endTime:  dayjs().format('YYYY-MM-DD') +' 23:59:59',
          dayStart: dayjs().format('YYYY-MM-DD') + ' 00:00:00',
          dayEnd:  dayjs().format('YYYY-MM-DD') +' 23:59:59',
          projectId: id,
          instrId: projectInstrId,
          damId: projectDamId,
          sort: projectSort,
          codeAutoList: [0, 1],
          valueTypeList: [0, 1, 2],
          valueStatusList: [0, 1, 2],
          valueCheckList: [0, 1]
        }
      } else {
        this.lastDataParams = {}
      }
    },
    // 打开上期数据
    openLastData () {
      this.lastDataVisible = true;
      this.$nextTick(_ => {
        this.setLastDataParams()
      })
    },
    // 计算
    calculate () {
      const sourceData = this.data.filter((item, i) => this.dataHasChangedList[i]);
      console.log('335', sourceData, this.dataHasChangedList)
      if (sourceData.length === 0) {
        uni.showToast({
          title: '数据未发生变化，编辑数据后进行计算！',
          icon: 'none'
        });
        return false;
      }
      this.calculating = true
      uni.showLoading({
        title: '计算中'
      });
      const data = sourceData.map(item => ({
        id: item.ID,
        codeId: item.CODE_ID,
        watchTime: item.WATCH_TIME,
        // valueIsAuto: this.entryFilter.codeIsAuto ? 1 : 0, // 自动化类型 VALUE_IS_AUTO（0人工，1自动化）
        valueIsAuto: 0, // 自动化类型 VALUE_IS_AUTO（0人工，1自动化）
        valueList: this.info.map(v => ({
          status: item.VALUE_STATUS,
          value: item[v.valueVectorFieldName],
          vectorId: v.valueVectorId
        }))
      }));
      calculateValueData(data)
        .then(res => {
          if (res.statusCode !== 200) {
            uni.showToast({
              title: res?.errMsg || '计算失败',
              icon: 'none',
              duration: 2000
            });
            setTimeout(() => {
              uni.hideToast();
            }, 2000);
            return false;
          }
          const { valueRecordList, errorInfo } = res.data;
          if (errorInfo) {
            uni.showToast({
              title: errorInfo,
              icon: 'none',
              duration: 2000
            });
            setTimeout(() => {
              uni.hideToast();
            }, 2000);
            return false;
          }
          if (!Array.isArray(valueRecordList)) {
            return false;
          }
          this.data.forEach((item, i) => {
            const valueRecord = valueRecordList.find(record => record.codeId === item.CODE_ID)
            if (valueRecord && Array.isArray(valueRecord.valueList)) {
              // 将计算结果显示在表格中
              valueRecord.valueList.forEach(({ vectorId, value }) => {
                const { valueVectorFieldName } = this.info.find(v => v.valueVectorId === vectorId)
                if (item[valueVectorFieldName] !== value) {
                  item[valueVectorFieldName] = value
                  this.dataHasChangedList[i] = true
                }
              })
            }
          })
          this.dataSetting.data = this.data
          this.$nextTick(_ => {
            // #ifdef H5
            this.$refs.inputTable?.hotInstance && this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting);
            // #endif
            // #ifdef APP-PLUS
            this.$refs.myTable && this.$refs.myTable.updateSettings(this.dataSetting);
            // #endif
          });
        }).catch(e => {
          return e;
        }).finally(e => {
          uni.hideLoading();
          this.calculating = false
        })
    },
    initData () {
      this.tableShow = false;
      getEntryValueData(this.project, dateFormat(this.currentDate, 'YYYY-MM-DD [00:00:00]'))
        .then(res => {
          const { data, info } = res.data
          if (Array.isArray(data) && Array.isArray(info)) {
            // 初始化每行的修改状态
            this.dataHasChangedList = data.map(_ => false)
            const rowHeaders = data.map(item => item.CODE_NAME)
            // #ifdef H5
            const rowHeaderWidth = Math.max(
              ...rowHeaders.map(name => getTextWidth(name, 14) + 5 * 2) // padding + border = 5
            )
            // #endif
            // #ifdef APP-PLUS
             const rowHeaderWidth = 80;
            // #endif
            this.data = data
            this.info = info
            // const rowHeaders = data.map(_ => codeName)
            // const colHeaders = ['日期', vectorName]
            const colHeaders = info.map(item => item.valueVectorName)
            this.dataSetting = {
              // language: HANDSONTABLE_LANGUAGE,
              data,
              columns: info.map(item => ({ data: item.valueVectorFieldName })),
              colHeaders,
              rowHeaders,
              // rowHeaderWidth: Math.max(rowHeaderWidth, 50),
              // #ifdef  H5
              rowHeaderWidth: this.getRowHeaderWidth(rowHeaders),
              // #endif
              // #ifdef  APP-PLUS
              // rowHeaderWidth: 80
              rowHeaderWidth: this.getRowHeaderWidth(rowHeaders) + 30,
              colHeaderWidth: this.getColHeaderWidth(colHeaders) + 30,
              // #endif
              reg: '数字',
              beforeChange: changes => {
                for (const change of changes) {
                  const newValue = change[3]
                  if (newValue && !/^\d+(\.\d+)?$/.test(newValue)) {
                    uni.showToast({
                      title: '请输入数字类型',
                      icon: 'none'
                    });
                    return false
                  }
                  return true;
                }
              },
              afterChange: changes => {
                if (Array.isArray(changes)) {
                  // #ifdef  H5
                  changes.forEach(([rowIndex, fieldName, oldValue, newValue]) => {
                    if (oldValue !== newValue) {
                      this.dataHasChangedList[rowIndex] = true
                    }
                  })
                  // #endif
                  // #ifdef  APP-PLUS
                  changes.forEach(({rowIndex, fieldName, oldValue, newValue}) => {
                    if (oldValue !== newValue) {
                      this.dataHasChangedList[rowIndex] = true
                    }
                  })
                  // #endif
                }
              }
            }
            this.oldDataSetting = {
              data: JSON.parse(JSON.stringify(data)),
              columns: info.map(item => ({ data: item.valueVectorFieldName })),
            }
          } else {
            this.data = []
            this.info = []
            this.dataHasChangedList = []
            this.dataSetting = { data: [] }
          }
          this.tableShow = true;
          this.$nextTick(_ => {
            // #ifdef H5
            this.$refs.inputTable?.hotInstance && this.$refs.inputTable.hotInstance.updateSettings(this.dataSetting);
            // #endif
            // #ifdef APP-PLUS
            this.$refs.myTable && this.$refs.myTable.updateSettings(this.dataSetting);
            // #endif
          });
        }).catch(e => {
          console.log(e)
        })
    },
    async getProject () {
      const { id } = this.option;
      const { data } = await getProjectById(id);
      this.project = data;
      await this.currentDateChange()
    },
    checkToolbarVisible () {
      this.toolbarVisible = this.hasScrolledToTop()
    }
  },
  // #ifdef H5
  beforeDestroy () {
    window.removeEventListener('scroll', this.checkToolbarVisible)
  }
  // #endif
}
</script>
<style lang="scss" scoped>
@import "./input.scss";
.monitor-container {
  background-color: #f5f5f5;
  .monitor-table-style {
    min-height: calc(70vh - 42px);
  }
}
</style>
