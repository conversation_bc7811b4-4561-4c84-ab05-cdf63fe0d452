

const fs = require('fs');
const path = require('path');

const inputPath = path.join(__dirname, 'src', 'data', 'yuan-coordinate.txt');
const outputPath = path.join(__dirname, 'src', 'data', 'yuan-coordinate.json');

fs.readFile(inputPath, 'utf8', (err, data) => {
  if (err) {
    console.error("Error reading the file:", err);
    return;
  }

  const lines = data.split('\n');
  const jsonData = [];

  lines.forEach(line => {
    if (line.trim() === '') {
      return;
    }

    try {
      const parts = line.split('\t');
      if (parts.length < 3) {
        return;
      }
      
      const timeStr = parts[0];
      const jsonStr = parts[parts.length - 1];
      const coords = JSON.parse(jsonStr);

      if (coords.latitude && coords.longitude) {
        jsonData.push({
          time: timeStr,
          latitude: coords.latitude,
          longitude: coords.longitude
        });
      }
    } catch (e) {
      // Ignore lines that cannot be parsed
    }
  });

  fs.writeFile(outputPath, JSON.stringify(jsonData, null, 2), 'utf8', (err) => {
    if (err) {
      console.error("Error writing the JSON file:", err);
      return;
    }
    console.log('Successfully converted to JSON:', outputPath);
  });
});

