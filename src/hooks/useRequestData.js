import { reactive, onMounted, toRef } from '@vue/composition-api';
import useRequest from './useRequest';

export default (api, ...params) => {
  const [handleRequest, loading] = useRequest(api);
  const state = reactive({
    data: {},
  });

  const getData = async (...params) => {
    if (!api) {
      return;
    }

    try {
      let resp = await handleRequest(...params);
      state.data = resp || {};
    }
    catch (e) {
      console.log(e);
    }
  }

  onMounted(async () => {
    await getData(...params);
  });

  return [toRef(state, 'data'), getData, loading];
}