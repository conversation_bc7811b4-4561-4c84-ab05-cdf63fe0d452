<template>
  <div class="px-4 pb-4">
    <!-- 头部 -->
    <div class="my-5 text-center">
      <img src="@/assets/images/event/safe.png" alt="加载失败">
      <div class="mt-2">当前暂无应急事件</div>
      <fks-button
        type="primary" class="mt-1" size="small"
        @click="$emit('update:showEventDailog',true)">
        添加应急事件
      </fks-button>
    </div>
    <!-- end -->
    <!-- 应急物资  应急队伍 -->
    <div>
      <common-title title="应急概况" class="mb-4 "/>
      <fks-col
        v-for="(prop,index) in ['goodsCount','teamCount']"
        class="d-flex align-items-center "
        :span="12" :key="index"
      >
        <div class="bg-primary p-2 rounded-5">
          <i :class="`iconfont icon-${index ? 'ren3' : 'bumen1' } text-white`" />
        </div>
        <div class="ml-2">
          <div class="font-weight-bold">
            <span class="text-17">{{ statisticsInfo[prop] ? statisticsInfo[prop].count : '-' }}/</span>
            <span class="text-14">{{ statisticsInfo[prop] ? statisticsInfo[prop][index ? 'team' : 'category'] : '-' }}</span>
          </div>
          <div class="text-13 text-gary">
            {{ index ? '应急人数/应急队伍' : '物资数量/物资类别' }}
          </div>
        </div>
      </fks-col>
    </div>
    <!-- end -->
    <!-- echart -->
    <div>
      <el-radio-group class="mt-4" v-model="viewType" size="small" @change.native="reViewChart" >
        <el-radio-button :label="1">应急队伍</el-radio-button>
        <el-radio-button :label="2">应急仓储</el-radio-button>
      </el-radio-group>
      <div v-loading="loading">
        <bar-chart ref="echart" :type="viewType" :chartData="statisticsInfo"/>
      </div>
    </div>
    <!-- 历史应急事件 -->
    <div>
      <common-title title="历史应急事件" class="mb-4 "/>
      <page-table
        @row-click="handleViewMap"
        ref="eventTable"
        class="my-2 support-click"
        height="240px"
        operateWidth="80"
        tableSize="mini"
        align="center"
        isShowPager
        :tableData="historyEventData"
        :columnFormat="column"
        :reqUrl="`${EMERGENCY_URL}/emergency-events`"
        :operate="{ check: true }"
        @check="toDetail"
        @handleCurrentChange="getEventData"
        @handleSizeChange="getEventData"
      />
    </div>
  </div>
</template>

<script>

import barChart from './barChart';
import pageTable from '@/components/Emergency/prepare/drillManage/pageTable.vue';
import { EMERGENCY_URL } from '@/plugins/constants/path';
import { mapActions } from 'vuex';
import eventUtil from '@/mixins/eventUtil';
import { toggleEventPoint } from '../SceneMap/mapControl';

export default {
  mixins: [eventUtil],
  props: {
    showEventDailog: {
      type: Boolean,
      default: false
    }
  },
  components: {
    barChart,
    pageTable
  },
  data() {
    return {
      loading: false,
      EMERGENCY_URL: EMERGENCY_URL,
      viewType: 1,
      historyEventData: [],
      column: [{
        label: '事件名称',
        prop: 'eventName',
        minWidth: '150px'
      }, {
        label: '事件类别',
        prop: 'emergencyEventName',
        minWidth: '100px'
      }, {
        label: '发生时间',
        prop: 'emergencyEventTime',
        minWidth: '120px'
      }],
      eventSurveySet: [{
        label: '物资数量/物资类别',
        prop: 'goodsCount'
      }, {
        label: '物资数量/物资类别',
        prop: 'goodsCount'
      }],
      statisticsInfo: {}
    };
  },
  mounted() {
    this.initData();
    this.getEventData();
  },
  methods: {
    ...mapActions('emergency', [
      'getPrepareStatistic'
    ]),
    handleViewMap(row) {
      toggleEventPoint(row, this.getEventColor);
      this.twinkleCircle();
    },
    reViewChart() {
      this.$refs.echart.setData(this.statisticsInfo[this.viewType === 1 ? 'teamCount' : 'goodsCount']);
    },
    // 获取应急历史事件
    getEventData() {
      this.$refs['eventTable'].getTableData().then(content => {
        this.historyEventData = content.map(item => {
          return {
            ...item,
            influenceEquipmentsName: item.influenceEquipments.map(info => {
              return info.capitalName;
            }).toString()
          };
        });
      });
    },
    // 查看详情
    toDetail(row) {
      this.$router.push(`/Emergency/eventHistory/eventDetail?eventId=${row.emergencyEventId}`);
    },
    initData() {
      this.$nextTick(() => {
        this.loading = true;
        this.getPrepareStatistic().then((data) => {
          this.statisticsInfo = data;
          this.reViewChart();
        }).finally(() => {
          this.loading = false;
        });
      });
    }
  },
  beforeDestroy() {
    self.timer && clearInterval(self.timer);
  }
};
</script>
