.flow-editor-node {

  &.approval,
  &.condition,
  &.end,
  &.start {
    position: relative;
    width: 200px;
    height: 150px;
  }

  div {
    box-sizing: border-box;
  }


  .flow-editor-node-container {
    position: absolute;
    top: 68px;
    left: 0;
    width: 200px;
    max-height: 92px;
    z-index: 2;
    background-color: #5b91fe;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;

    .node-title {
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 3px 13px;

      .node-title-name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .node-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-grow: 1;
      margin: 0 4px 4px;
      padding: 8px;
      background: #fff;
      border-radius: 4px;
      min-height: 38px;
      font-size: 14px;
      color: #3e4759;
      cursor: pointer;

      &.node-content-placeholder {
        color: #a1a5ad;
      }
    }
  }

  &.end .flow-editor-node-container,
  &.start .flow-editor-node-container {
    background: #a9b4cd;
  }

  &.condition {
    .flow-editor-node-container {
      background: #fff;

      .node-title {
        color: #2eb795;
        padding-right: 24px;
        border-bottom: 1px solid #f6f6f7;
      }
    }
  }

  &.approval .flow-editor-node-container,
  &.condition .flow-editor-node-container,
  &.end .flow-editor-node-container,
  &.start .flow-editor-node-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    max-height: 92px;
    z-index: 2;
  }

  &.approval .bottom-v-line,
  &.condition .bottom-v-line,
  &.end .bottom-v-line,
  &.start .bottom-v-line {
    position: absolute;
    bottom: 0;
    left: 99px;
    height: 150px;
    width: 2px;
    background-color: #c2c5cc;
  }

  &.approval .add-node-btn,
  &.condition .add-node-btn,
  &.end .add-node-btn,
  &.start .add-node-btn {
    position: absolute;
    bottom: 26px;
    left: 89px;
    width: 23px;
    height: 23px;
    border-radius: 50%;
  }

  .add-node-btn {
    position: relative;
    width: 23px;
    height: 23px;
    border-radius: 50%;
    cursor: pointer;
    .add-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 5px solid #c1d6ff;
      background-color: #fff;
      color: #37f;
      outline: none;
      cursor: pointer;
    }
    &:hover {
      .add-btn {
        // border: 4px solid #72a1ff;
        color: rgb(0, 45, 136);
      }
    }
  }

  &.route {
    display: flex;
    flex-direction: column;
  }

}


.flow-editor-node.branch>.nodes {
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 路由节点连线
.flow-editor-node.route>.bottom-h-line {
  height: 2px;
  background-color: #c2c5cc;
}

.flow-editor-node.route>.bottom-v-line {
  height: 100px;
  width: 2px;
  background-color: #c2c5cc;
  align-self: center;
}

// 路由节点纵向连线
.flow-editor-node.route .flow-editor-node.branch>.bottom-v-line {
  display: block;
  width: 2px;
  background-color: #c2c5cc;
  flex-grow: 1;
}

// 路由节点
// 添加条件按钮
.flow-editor-node.route>.add-node-btn {
  margin-top: -61.5px;
  margin-bottom: 38.5px;
  align-self: center;
}

.flow-editor-node.route>.add-branch {
  position: relative;
  height: 28px;
  margin-bottom: 16px;
  border-radius: 14px;
  border: 1px solid transparent;
  align-self: center;
  color: #37f;
  font-size: 12px;
  cursor: pointer;
  z-index: 1;
}

.flow-editor-node.route>.add-branch .add-branch-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 10px;
  border: 4px solid #c1d6ff;
  border-radius: 14px;
  background-color: #fff;
}

.flow-editor-node.branch {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 0 50px;
  min-height: 100%;
}

// braches
.flow-editor-node.route>.branches {
  display: flex;
  justify-content: center;
}

.flow-editor-node.route>.top-h-line {
  height: 2px;
  background-color: #c2c5cc;
  margin-top: 13px;
  margin-bottom: -15px;
}


// 路由横向链接线遮罩
.flow-editor-node.route .flow-editor-node.branch:first-child>.top-line-mask {
  top: -31px;
  left: 0;
}

.flow-editor-node.route .flow-editor-node.branch:last-child>.top-line-mask {
  top: -31px;
  right: 0;
}

.flow-editor-node.route .flow-editor-node.branch:first-child>.bottom-line-mask {
  bottom: -2px;
  left: 0;
}

.flow-editor-node.route .flow-editor-node.branch:last-child>.bottom-line-mask {
  bottom: -2px;
  right: 0;
}

.flow-editor-node.route .flow-editor-node.branch>.top-v-line {
  display: block;
  position: absolute;
  top: -31px;
  left: calc(50% - 1px);
  width: 2px;
  height: 31px;
  background-color: #c2c5cc;
}

.flow-editor-node.route .flow-editor-node.branch:first-child>.bottom-line-mask,
.flow-editor-node.route .flow-editor-node.branch:first-child>.top-line-mask,
.flow-editor-node.route .flow-editor-node.branch:last-child>.bottom-line-mask,
.flow-editor-node.route .flow-editor-node.branch:last-child>.top-line-mask {
  display: block;
  position: absolute;
  width: 50%;
  height: 2px;
  background-color: #f0f0f2;
}
.flow-editor-node .flow-editor-node-container:hover>.delete-btn {
  display: block;
}
.flow-editor-node .flow-editor-node-container>.delete-btn {
  display: none;
  position: absolute;
  top: 4px;
  right: 5px;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
  &:hover {
    // color: #ff8282;
  }
}
.flow-editor-node.condition .delete-btn {
  color: #a1a5ad;
}
.flow-editor-node.has-error .flow-editor-node-container {
  border: 2px solid #ff5b4c;
  box-shadow: 0 0 3px 0 #ff5b4c;
  
}
.flow-editor-node.has-error {
  .warning-node-icon {
    display: block;
    position:absolute;
    right:-32px;
    top:0px;
    color:#ff5b4c;
    text-align:center;
    line-height:30px;
    font-size:24px;
    font-weight:bold;
  }
}

.root-node-warp {
  a,
  address,
  article,
  aside,
  blockquote,
  body,
  code,
  dd,
  div,
  dl,
  dt,
  fieldset,
  figcaption,
  figure,
  footer,
  form,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  header,
  hr,
  html,
  iframe,
  img,
  input,
  legend,
  li,
  main,
  nav,
  ol,
  p,
  pre,
  section,
  span,
  td,
  textarea,
  th,
  ul {
    margin: 0;
    padding: 0;
  }

  .flow-editor-node.branch>.bottom-line-mask,
  .flow-editor-node.branch>.bottom-v-line,
  .flow-editor-node.branch>.top-line-mask,
  .flow-editor-node.branch>.top-v-line {
    display: none;
  }
 &>.flow-editor-node.branch {
  margin: 0 auto; 
 }
}