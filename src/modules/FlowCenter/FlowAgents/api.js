/*
 * @Des: 流程代理人
 * @Author: jin_yc
 * @Date: 2019-11-11 11:18:51
 * @LastEditor: jin_yc
 * @LastEditTime: 2019-11-23 15:44:18
 */
import request from '@/utils/request'

export function getFlowAgentsList(params) {
  return request({
    method: 'get',
    url: '/sys-bpm/agents',
    params: params
  })
}

export function addFlowAgents(data) {
  return request({
    method: 'post',
    url: '/sys-bpm/agent',
    data: data
  })
}
export function saveFlowAgents(data) {
  return request({
    method: 'put',
    url: '/sys-bpm/agent',
    data: data
  })
}
export function deleteFlowAgents(id) {
  return request({
    method: 'delete',
    url: '/sys-bpm/agent',
    params: {
      id
    }
  })
}
