<template>
  <div class="container full-height d-flex flex-column">
    <statistic-card ref="statsCard" :queryForm="queryForm" class="cards mb-3"/>
    <fks-query-page
      v-loading="loading"
      :currentPage.sync="pageNo"
      :pageSize.sync="pageSize"
      :data="tableData"
      :page-sizes="[10, 15, 25, 50, 100]"
      :total="total"
      border
      class="table"
      layout="total, sizes, prev, pager, next, jumper"
      tableName="考勤统计"
      @clear="clear"
      @query="query"
    >
      <template slot="search">
        <fks-search-item label="所在部门">
          <fks-org-selector
            :deptId.sync="queryForm.orgNo"
            :deptName.sync="queryForm.orgName"
          />
        </fks-search-item>
        <fks-search-item label="人员">
          <stats-user-selector :form="queryForm"/>
        </fks-search-item>
        <fks-search-item :span="8" label="时段">
          <div class="d-flex">
            <fks-date-picker
              v-model="queryForm.date"
              :clearable="false"
              class="full-width"
              end-placeholder="结束月份"
              range-separator="~"
              start-placeholder="开始月份"
              type="monthrange"
            />
            <fks-checkbox v-model="queryForm.isLastMonth" class="ml-2">上月</fks-checkbox>
          </div>
        </fks-search-item>
        <fks-search-item :span="24" label="统计字段">
          <check-list :form="queryForm" @fieldLoad="fieldLoad"/>
        </fks-search-item>
      </template>
      <template slot="button">
        <fks-button type="primary" @click="handleExport">
          <i class="fks-icon-check mr-1"/>
          <span>导出列表</span>
        </fks-button>
      </template>
      <template>
        <fks-table-column fixed="left" label="员工号" prop="personNo"/>
        <fks-table-column fixed="left" label="姓名" prop="personName"/>
        <fks-table-column fixed="left" label="所在部门" prop="orgName"/>
        <fks-table-column
          v-for="item in appendColumns"
          :key="item.column"
          :label="item.column"
        >
          <fks-table-column
            v-for="el in item.subColumns"
            :key="el.label"
            :label="el.label"
            :prop="el.prop"
          >
            <template slot-scope="scope">
              {{ scope.row[el.prop] }}
            </template>
          </fks-table-column>
        </fks-table-column>
        <fks-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <fks-button type="text" @click="handleClick(scope.row)">详情</fks-button>
          </template>
        </fks-table-column>
      </template>
    </fks-query-page>
    <export-form ref="exportForm" :filterForm="queryForm"/>
  </div>
</template>
<script>
import cloneDeep from 'lodash/cloneDeep';
import StatisticCard from './components/StatisticCard.vue';
import ExportForm from './components/ExportForm.vue';
import StatsUserSelector from './components/UserSelector.vue';
import CheckList from './components/CheckList.vue';
import {
  DEFAULT_FORM_DATA,
  formatStartAndEndDate,
  getDates
} from '@/modules/FlowCenter/AttendanceManagement/Statistics/util';
import dayjs from 'dayjs';
import {getAttendanceStatsList} from '@/modules/FlowCenter/AttendanceManagement/api';

export default {
  name: 'Statistics',
  components: {StatsUserSelector, StatisticCard, ExportForm, CheckList},
  data() {
    return {
      cardInfo: [],
      loading: false,
      showUserSelector: true,
      tableData: [],
      appendColumns: [],
      pageSize: 10,
      pageNo: 1,
      total: 0,
      queryForm: cloneDeep(DEFAULT_FORM_DATA)
    };
  },
  methods: {
    clear() {
      this.queryForm = cloneDeep(DEFAULT_FORM_DATA);
    },
    query() {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.generateParams()
      };
      this.$refs.statsCard.getData();
      this.getTableData(params);
    },
    getTableData(params) {
      this.loading = true;
      getAttendanceStatsList(params).then(res => {
        if (res.status) {
          this.total = res.data.total;
          if (res.data.list.length) {
            this.appendColumns = this.formatColumns(res.data.list);
            this.tableData = this.formatTableData(res.data.list);
          }
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    formatColumns(sourceData) {
      return sourceData[0].monthStatisticsList.map(item => {
        return {
          column: item.title,
          subColumns: item.statisticsItemList.map(el => {
            return {
              label: el.title,
              prop: `${item.title}-${el.title}`
            };
          })
        };
      });
    },
    formatTableData(sourceData) {
      return sourceData.map(item => {
        const {monthStatisticsList, ...rest} = item;
        let dataObj = {};
        monthStatisticsList.forEach(column => {
          column.statisticsItemList.forEach(i => {
            dataObj[`${column.title}-${i.title}`] = i.duration;
          });
        });
        return {
          ...rest,
          ...dataObj
        };
      });
    },
    handleExport() {
      this.$refs.exportForm.visible = true;
    },
    generateParams() {
      return {
        ...formatStartAndEndDate(this.queryForm.date),
        fields: this.queryForm.fields.join(','),
        orgNo: this.queryForm.orgNo,
        userNames: this.queryForm.username
      };
    },
    fieldLoad() {
      const params = {
        pageNo: 1,
        pageSize: this.pageSize,
        ...this.generateParams()
      };
      this.getTableData(params);
    },
    handleClick(row) {
      this.$router.push({
        path: '/flowCenter/attendanceManagement/statistics/statsDetail',
        query: {
          punchCardUserFullname: row.personName,
          userName: row.userName,
          punchCardUserNumber: row.personNo,
          date: this.queryForm.date.map(date => dayjs(date).format('YYYY-MM')).join(','),
          orgNo: this.queryForm.orgNo,
          orgName: this.queryForm.orgName
        }
      });
    }
  },
  watch: {
    'queryForm.isLastMonth': function (newVal) {
      this.queryForm.date = getDates(newVal);
    }
  }
};
</script>
<style lang="scss" scoped>
.container {
  .cards {
    flex: 0 1 auto;
  }

  .table {
    flex: 1 1 auto;
  }
}
</style>
