<template>
  <div id="inspect-map"/>
</template>

<script>
import mapboxgl from 'mapbox-gl';
import mapMixin from '@/mixins/mapMixin';
import HdecTransform from 'hdec-transform/dist/HhdcTransform.js';

const url = {
  '水库': 'reservoir-green',
  '泵站': 'pumpingstation-green',
  '水厂': 'waterworks-green',
  '水池': 'pool-green',
  '高位水池': 'reservoir-green'
};

export default {
  name: 'MapboxDemo',
  props: ['points', 'capitalModels'],
  mixins: [mapMixin('inspect-map', [
    'jianzao_yxst_stake',
    'jianzao_yxst_pipeline_sb',
    'jianzao_yxst_permanentland_sb',
    'jianzao_yxst_trunkdivision_symbol'
  ])],
  data() {
    return {
      mode: 'tianditu',
      markers: new Set()
    };
  },
  methods: {
    fitBounds(coordinates) {
      // 计算边界范围 (bounds)
      const bounds = new mapboxgl.LngLatBounds(
        coordinates[0], // 初始点
        coordinates[0] // 初始化为同一点
      );

      // 扩展边界范围以包含所有坐标
      for (const coord of coordinates) {
        bounds.extend(coord);
      }
      // 调整地图视图，使轨迹线完全显示
      this.map.fitBounds(bounds, {
        padding: 20, // 添加内边距，避免轨迹线贴近边界
        maxZoom: 18, // 设置最大缩放级别，防止过于放大
        duration: 1000 // 动画时间，单位毫秒
      });
    },
    createMarker({objName, longitude, latitude, type}) {
      const div = document.createElement('div');
      div.style.display = 'flex';
      div.style.flexDirection = 'column';
      div.style.alignItems = 'center';
      div.style.justifyContent = 'center';
      if (url[type]) {
        div.innerHTML = `
          <img src="${require(`@/modules/GisService/rx-app/src/assets/image/base/${url[type]}.png`)}" alt="#" height="30px" />
          <span style="color: #eeeeee;font-size: 12px;max-width: 100px">${objName}</span>
        `;
      } else {
        div.innerHTML = `
          <img src="${require('@/assets/images/GIS/orange.png')}" alt="#" height="30px" />
          <span style="color: #eeeeee;font-size: 12px;max-width: 100px">${objName}</span>
        `;
      }
      const marker = new mapboxgl.Marker(div)
        .setLngLat([longitude, latitude]) // 设置标记位置 [经度, 纬度]
        .addTo(this.map); // 将标记添加到地图
      this.markers.add(marker);
    },
    // 新增创建点标记方法
    createPositionMarker(lnglat, text, bgColor) {
      const markerEl = document.createElement('div');
      markerEl.className = 'map-point-marker';
      Object.assign(markerEl.style, {
        width: '30px',
        height: '30px',
        borderRadius: '50%',
        background: bgColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '14px',
        border: '2px solid white'
      });
      markerEl.textContent = text;

      const marker = new mapboxgl.Marker(markerEl)
        .setLngLat(lnglat)
        .addTo(this.map);
      this.markers.add(marker);
    },
    addMarkers() {
      // 展示巡检对象
      const validPoints = this.points
        .filter((point) => point.smx && point.smy)
        .map(point => {
          return {
            objName: point.capitalName,
            type: point.kindName,
            longitude: +point.smx,
            latitude: +point.smy
          };
        });
      for (let i = 0; i < validPoints.length; i++) {
        this.createMarker(validPoints[i]);
      }
    },
    drawInspectLine(coordinates) {
      // 如果有旧数据，则覆盖
      const source = this.map.getSource('route');
      if (source) {
        // 如果之前有设置数据源，则重新覆盖即可
        source.setData({
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates
          }
        });
        this.map.moveLayer('route-line');
      } else {
        // 如果没有设置数据源，则设置数据源
        this.map.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates
            }
          }
        });
        // 添加 LineLayer 绘制轨迹线
        this.map.addLayer({
          id: 'route-line',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#84afec', // 线的颜色
            'line-width': 6 // 线的宽度
          }
        });
        this.map.moveLayer('route-line');
      }
    },
    paintRoadMap(coordinates) {
      this.drawInspectLine(coordinates);
      // 新增起点终点标记
      if (coordinates.length >= 2) {
        // 清除旧标记（如果存在）
        document.querySelectorAll('.map-point-marker').forEach(el => el.remove());

        // 起点（第一个坐标）
        this.createPositionMarker(coordinates[0], '起', '#52c41a');
        // 终点（最后一个坐标）
        this.createPositionMarker(coordinates[coordinates.length - 1], '终', '#ff4d4f');
      }
      console.info('🚀🚀', 'this.capitalModels -->', this.capitalModels, `<-- index.vue/paintRoadMap`)
      this.addSymbolLayer(this.capitalModels);
      this.fitBounds(coordinates);
    },
    async onMapLoad(mapInstance) {
      // 加载所有图标
      const iconPromises = Object.entries(url).map(async ([type, name]) => {
        try {
          const image = await this.loadImage(
            require(`@/modules/GisService/rx-app/src/assets/image/base/${name}.png`)
          );
          mapInstance.addImage(type, image);
        } catch (error) {
          console.error(`Failed to load icon ${type}:`, error);
        }
      });

      // 加载默认图标
      const defaultIcon = await this.loadImage(require('@/assets/images/GIS/orange.png'));
      mapInstance.addImage('default-marker', defaultIcon);
      await Promise.all(iconPromises);
    },
    afterMapLoad() {
      this.paintRoadMap(this.points);
    },
    addSymbolLayer(points) {
      // 生成 GeoJSON 数据源
      const geojson = {
        type: 'FeatureCollection',
        features: points.map(point => {
          const {capitalModel} = point;
          // const [x, y] = HdecTransform.gcj02Twgs84(point.codeX, point.codeY);
          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [+capitalModel.smx, +capitalModel.smy]
            },
            properties: {
              title: capitalModel.capitalName,
              type: capitalModel.kindName
            }
          };
        })
      };

      // 添加数据源
      if (this.map.getSource('markers')) {
        this.map.getSource('markers').setData(geojson);
      } else {
        this.map.addSource('markers', {
          type: 'geojson',
          data: geojson,
          cluster: true, // 启用聚类
          clusterRadius: 20 // 聚类半径
        });
      }

      // 添加图标图层
      if (!this.map.getLayer('marker-icons')) {
        this.map.addLayer({
          id: 'marker-icons',
          type: 'symbol',
          source: 'markers',
          layout: {
            'icon-image': ['get', 'type'], // 根据类型使用不同图标
            'icon-allow-overlap': false, // 禁止图标重叠
            'icon-ignore-placement': true, // 允许文字重叠图标
            'text-field': ['get', 'title'],
            'text-font': ['Open Sans Semibold'],
            'text-size': 12,
            'text-offset': [0, 0.6],
            'text-anchor': 'top'
          },
          paint: {
            'text-color': '#eeeeee'
          }
        });
      }

      // 添加聚类图层
      if (!this.map.getLayer('clusters')) {
        this.map.addLayer({
          id: 'clusters',
          type: 'circle',
          source: 'markers',
          filter: ['has', 'point_count'],
          paint: {
            'circle-color': '#52c41a',
            'circle-radius': 20,
            'circle-opacity': 0.6
          }
        });

        this.map.addLayer({
          id: 'cluster-count',
          type: 'symbol',
          source: 'markers',
          filter: ['has', 'point_count'],
          layout: {
            'text-field': '{point_count_abbreviated}',
            'text-font': ['Open Sans Semibold'],
            'text-size': 12
          }
        });
      }
    }
  },
  beforeDestroy() {
    // 清除所有标记
    this.markers.forEach(marker => marker.remove());
    this.markers.clear();
  }
};
</script>
