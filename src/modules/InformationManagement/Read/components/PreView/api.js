
import request from '@/utils/request'
import axiosexport from '@/utils/export'


export function getFileAsPdf(fileToken) {
  return axiosexport({
    method: 'get',
    url: '/sys-storage/file/pdf',
    responseType:'blob',
    params: {
      fileToken,
    },
  }).catch((e)=>{
    if(e) console.log(e.toString())
  })
}


export function getPic(fileToken) {
  return axiosexport({
    method: 'get',
    url: '/sys-storage/download_image',
    responseType:'blob',
    params: {
      f8s:fileToken,
    },
  }).catch((e)=>{
    if(e) console.log(e.toString())
  })
}


