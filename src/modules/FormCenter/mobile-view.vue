<template>
  <div class="main-container" style="height: 100%">
    <fm-nav-bar
      :border="false"
      :left-arrow="hasLeftArrow"
      left-text=""
      right-text=""
      @click-left="goBack"
    >
      <template slot="title">
        <div class="flex col-baseline">
          <span>{{ formName }}</span>
          <i v-if="isNotifyButtonKey" class="m-l-16 fks-icon-info-outline" style="color: #999999;font-size: 16px" @click="handleTipClick" />
        </div>
      </template>
    </fm-nav-bar>
    <fm-tabs v-if="type !== 'add'" line-width="80px" @tab-click="tabClick" v-model="tab">
      <template v-for="(item, index) in tabTitles">
        <fm-tab
          v-if="
            item !== '委托' &&
            (['XC_DRIVER_CAR_MODIFY', 'XC_FORM_MODIFY', 'XC_CAR_COMP_MODIFY'].includes(
              $route.query.buttonKey
            )
              ? item !== '审批'
              : true)
          "
          :key="index"
          :label="item"
        ></fm-tab>
      </template>
    </fm-tabs>
    <!-- 从业务模块发起时只显示表单详情和暂存、提交 -->
    <div v-if="type === 'add' || type === 'draft'" class="component task-detail-form">
      <component
        :is="dynamicForm"
        ref="formTemp"
        :bizId="bizId"
        :taskKey="taskKey"
        :type="type"
        :currentButtonKey="currentButtonKey"
        v-bind="$attrs"
        @setEntityName="setEntityName"
        @setFormName="setFormName"
        @setModelKey="setModelKey"
        @setPreSaveValidateProps="setPreSaveValidateProps"
        @setService="setService"
        @setSubmitText="setSubmitText"
        @setReturnNodeList="setReturnNodeList"
        @init-success="showApproveBtn = true"
        :isCrossNodeReturn="isCrossNodeReturn"
      />
      <approve-buttons
        v-show="tab === 0 && showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :type="type"
        :biz-id="bizId"
        :currentButtonKey="currentButtonKey"
        :submit-text.sync="submitText"
        :disable-submit="projectClosed"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
      <div v-show="tab === 1" class="content-container">
        <flow-timeline :bizId="bizId" :taskId="taskId"></flow-timeline>
      </div>
    </div>
    <!-- 任务待办打开时 -->
    <div
      v-else
      :style="
        tab === 0
          ? [{ height: getHeight }, { ['overflow-y']: 'auto', ['overflow-x']: 'hidden' }]
          : {}
      "
    >
      <div
        v-show="tab === 0"
        class="content-container x2"
        :style="{ 'margin-bottom': tabTitles.length > 2 ? '0px' : 0 }"
      >
        <component
          :is="dynamicForm"
          ref="formTemp"
          :bizId="bizId"
          :taskKey="taskKey"
          :type="type"
          :currentButtonKey="currentButtonKey"
          v-bind="$attrs"
          @setEntityName="setEntityName"
          @setFormName="setFormName"
          @setModelKey="setModelKey"
          @setPreSaveValidateProps="setPreSaveValidateProps"
          @setService="setService"
          @setSubmitText="setSubmitText"
          @setReturnNodeList="setReturnNodeList"
          @init-success="showApproveBtn = true"
          :isCrossNodeReturn="isCrossNodeReturn"
        />
      </div>
      <approve-buttons
        v-show="tab === 0 && type === 'execute' && showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :type="type"
        :biz-id="bizId"
        :current-button-key="currentButtonKey"
        :submit-text.sync="submitText"
        :disable-submit="projectClosed"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        :currentButtonKey="currentButtonKey"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
    </div>
    <div v-show="tab === 1" class="content-container">
      <flow-timeline ref="timelineRef" :bizId="bizId" :taskId="taskId"></flow-timeline>
    </div>
    <div
      v-show="
        tab === 2 || (tab === 0 && (tabTitles.length === 2 ? !!$route.query.buttonKey : true))
      "
      class="content-container"
    >
      <flow-approval
        ref="flowApproval"
        :afterSubmit="afterSubmit"
        :aftereReject="afterReject"
        :beforeSubmit="beforeSubmit"
        :checkRules="checkRules"
        :detailParamList="detailParamList"
        :entityName="entityName"
        :formData="formData"
        :from.sync="from"
        :formName.sync="formName"
        :returnNodeList="returnNodeList"
        :getEntityParam="getEntityParam"
        :is-default="isDefault"
        :modelKey="flowData.modelKey"
        :submit-text.sync="submitText"
        :submitURL="service.submit"
        :taskKey="taskKey"
        :biz-id="bizId"
        :show-btn="false"
        :is-show="
          !!$route.query.buttonKey
            ? ![
                'XC_DRIVER_CAR_MODIFY',
                'XC_FORM_MODIFY',
                'XC_CAR_COMP_MODIFY',
                'FORM_MODIFY_ADMIN',
              ].includes($route.query.buttonKey)
            : tab === 2
        "
      >
      </flow-approval>
      <approve-buttons
        v-show="showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :biz-id="bizId"
        :submit-text.sync="submitText"
        :disable-submit="projectClosed"
        :type="type"
        :currentButtonKey="currentButtonKey"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
    </div>
    <div v-show="tab === 3" class="content-container">
      <flow-entrust :formData="formData"></flow-entrust>
    </div>
    <notification-confirm-mobile ref="notificationRef" />
  </div>
</template>
<script>
import mixins from './mixins'
import * as StateTypes from '@store/State/stateTypes'
import { getApprovalList } from './api'
import flowTimeline from './components/FlowTimeline/index.vue'
import flowEntrust from './components/FlowEntrust/index.vue'
import flowApproval from './components/FlowApproval/index.vue'
import CarApply from '@modules/FormCenter/CarApply/index.vue'
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
import NotificationConfirmMobile from '@components/NotificationConfirm/mobile-view.vue'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import { changeTips } from '@utils/constants'
import { mapState } from 'vuex'
export default {
  name: 'FormCenterMobileView',
  components: {
    NotificationConfirmMobile,
    CarApply,
    flowApproval,
    flowEntrust,
    flowTimeline,
    ApproveButtons,
  },
  mixins: [mixins, isConfirmXCMixin],
  computed: {
    getHeight() {
      if (this.tab === 0 && this.type === 'edit' && this.buttonList.length > 0) {
        return 'calc(100% - 149px)'
      } else {
        if (this.type === 'view') {
          return 'calc(100% - 88px)'
        } else {
          return 'calc(100% - 140px)'
        }
      }
    },
    ...mapState([StateTypes.PORTAL_STATUS_TABLE, StateTypes.IS_PROJECT_CLOSED]),
    projectClosed() {
      console.info('🚀🚀', 'this.$refs.formTemp -->', this.$refs.formTemp, `<-- mobile-view.vue/projectClosed`)
      if (this.$refs.formTemp) {
        console.info('🚀🚀', 'this.$refs.formTemp -->', this.$refs.formTemp, `<-- mobile-view.vue/projectClosed`)
        console.info('🚀🚀', 'this.$refs.formTemp -->', this.$refs.formTemp.formData, `<-- mobile-view.vue/projectClosed`)
      }
      if (this.formData.projectId) {
        return this[StateTypes.PORTAL_STATUS_TABLE][this.formData.projectId] === 200
      } else {
        return this[StateTypes.IS_PROJECT_CLOSED]
      }
    },
    hasLeftArrow() {
      return Boolean(this[StateTypes.APPLY_RESOURCE] !== 1)
    },
  },
  data() {
    return {
      tabTitles: [],
      tab: 0,
      from: {},
      formData: {},
      isDefault: true, // true 使用原来的请求参数格式 false不是使用
      detailParamList: [], // 需提交的明细表数据
      submitText: '提交',
      buttonList: [],
      returnNodeList: [],
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    setReturnNodeList(list) {
      this.returnNodeList = list;
      // 获得lastItem，写入
      const lastItem = Array.isArray(list) && list.length > 0 ? list[list.length - 1] : null;
      this.$refs.flowApproval.setDefaultReturnNode(lastItem);
    },
    handleTipClick() {
      const content = changeTips[this.currentButtonKey];
      this.$dialog.alert({message: content});
    },
    getComment() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.comment : ''
    },
    getReturnNode() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.returnedNode : ''
    },
    getButtonList(data) {
      this.buttonList = data
      this.formBtnList = data;
    },
    async init() {
      if (this.type !== 'execute') {
        this.tabTitles = ['详情', '流转']
        return false
      }
      let params = {
        taskId: this.taskId,
        bizId: this.bizId,
      }
      if (Object.keys(params).length === 0) {
        return false
      }
      const res = await getApprovalList(params)
      this.approvalList = res.data
      if (res.data?.at(-1).taskKey === 'UserTask_0') {
        this.tabTitles = ['详情', '流转']
        // 判断详情
        this.taskKey = res.data[res.data.length - 1].taskKey
      } else {
        this.tabTitles = ['详情', '流转', '审批', '委托']
      }
      // 判断退回
      if (res.data.length > 1 && res.data?.at(-2).approveState === 'reject') {
        if (res.data?.at(-1).taskKey === 'UserTask_0') {
          // this.type = 'edit'
          // this.$route.params.type = 'edit'
        }
      }
      //判断暂存
      if (
        res.data.length === 1 &&
        res.data?.at(-1).taskKey === 'UserTask_0' &&
        res.data?.at(-1).approveState === 'stage'
      ) {
        // this.type = 'edit'
        // this.$route.params.type = 'edit'
      }
    },
    goBack() {
      this.type === 'add'
        ? this.$router.push({ path: '/projectCar/projectPortal/carRecord' })
        : this.$router.go(-1)
      // this.$router.go(-1);
    },
    tabClick(val) {
      this.tab = val
    },
    getEntityParam(item) {
      return this.$refs.formTemp?.getEntityParam({
        ...item,
        targetKey: 'submit',
        customId: this.customId,
      })
    },
    // 提交流程后的操作
    afterSubmit() {},
    // 表单退回之后的操作
    afterReject() {},
    // 提交流程前钩子函数
    beforeSubmit() {
      this.$refs.formTemp?.beforeSubmit()
    },
    // 非空校验方法
    checkRules() {
      // 业务表单提供校验方法
      if (this.$refs.formTemp.validator) {
        return this.$refs.formTemp.validator()
      } else {
        return true
      }
    },
    setSubmitText(submitText) {
      this.submitText = submitText
      console.info('🚀🚀', 'this.$refs.formTemp -->', this.$refs.formTemp, `<-- mobile-view.vue/setSubmitText`)
    },
  },
  watch: {
    currentButtonKey: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.isNotifyButtonKey) {
          this.getNotification(newVal).then(res => {
            if (res === false) {
              this.$refs.notificationRef.open({buttonKey: newVal, checked: res});
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
@import './mobile-view';
</style>
