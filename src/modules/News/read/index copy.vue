<template>
  <div class="read" ref="read">
    <div class="read-content">
      <!--      文章内容部分-->
      <div
        class="read-content-article"
        :style="{
          height: isDialog ? 'calc(100% - 65px)' : 'calc(100% - 80px)',
        }"
      >
        <div v-if="!pre_view_file_control" class="article-text">
          <div class="article-text-title">
            <h3 style="text-align: center">
              {{ current_article.standardName }}
            </h3>
          </div>
          <div
            class="article-text-content"
            v-html="current_article.theText"
            ref="ARTICLE"
          ></div>
        </div>
        <!-- 附件预览-->
        <div v-else-if="pre_view_file_control" style="height: 100%">
          <PreView :file="current_file"></PreView>
        </div>
      </div>
    </div>
    <!--附件和评论区域-->
    <div class="files-and-comment">
      <div class="files">
        <div>
          <span style="font-size: 12px">
            <svg-icon icon-class="adjunct"></svg-icon>
          </span>
          <span class="file-title"
            >{{ LANG.ATTACHMENT }}·{{ showFiles.length }}</span
          >
        </div>
        <div class="file-list">
          <div v-show="showFiles.length">
            <section
              class="file-item"
              v-for="(file, index) in showFiles"
              :key="`file_${index}`"
              @mouseover="fileMoseOver"
              @mouseleave="fileMoseLeave"
            >
              <section class="file-img">
                <svg-icon
                  :icon-class="getFileIconByExtName(file.extName)"
                  class="file_icon"
                ></svg-icon>
              </section>
              <section class="file-describe">
                <span class="file-item-filename" @click="preViewFile(file)">{{
                  file.fileName
                }}</span>
                <label>{{
                  (Number(file.size) / 1024).toFixed(2) + " KB"
                }}</label>
              </section>
              <section class="file-operate">
                <section @click="preViewFile(file)">
                  <svg-icon icon-class="browse"></svg-icon>
                </section>
                <section @click="download(file)">
                  <svg-icon icon-class="download"></svg-icon>
                </section>
              </section>
            </section>
          </div>
          <div
            v-show="!showFiles.length"
            style="
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
            "
          >
            <h2>暂无附件</h2>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { OA_HOMEPAGE_ID } from '@/api/information'
import {
  getFileList,
  getFileList1,
  down,
  getDataByid
} from './api/index'
import { getNodeTree } from '@/api/information'
// import { CURRENT_ARTICLE } from '@/store/modules/CommonInfo/State/stateTypes'
import download from '@/api/downloadFile'
import mix from '@/mixins/module'
import PreView from './components/PreView/index'
import { fileTypeIcon } from './api/static_data'
export default {
  name: 'Read',
  mixins: [mix],
  props: {
    id: String,
    isDialog: {
      type: Boolean,
      default: false
    },
    // 从父组件获取dataItem数据
    dataItem: {
      type: Object,
      default:()=> {}
    }
  },
  computed: {
    input_row () {
      if (this.input_status) {
        return 5
      } else {
        return 1
      }
    },
    // article_id() {
    //   if (this.id) {
    //     return this.id
    //   }
    //   return (
    //     this.$store.state.CommonInfo[CURRENT_ARTICLE]['id'] ||
    //     this.$route.params.id
    //   )
    // }
  },
  components: { PreView },
  data () {
    return {
      current_article: {},
      fileList: [],
      communications: [],
      showFiles: [], //fileList过滤之后的文件
      fileFlag: '正文附件',
      comm_input: '',
      input_status: false,
      pre_view_file_control: false,//预览文件的切换，为true时切换为附件预览；
      current_file: '',//存储附件的token
      directory: '',
      notice_list: false
    }
  },
  methods: {
    closePreView () {
      this.pre_view_file_control = false
    },
    // 文件图标
    getFileIconByExtName (extName) {
      if (fileTypeIcon[extName]) {
        return fileTypeIcon[extName]
      }
      return fileTypeIcon['doc-blank']
    },
    stop (e) {
      e.stopPropagation()
    },
    // 正文pdf
    getFileList1 () {
      debugger
      let that = this;
      let f8s = [
        this.$attrs.value.mainAttachment
      ]
      let f8s3 = [
        this.$attrs.value.otherAttachment
      ]
      this.preViewFile(this.$attrs.value.mainAttachment)
      f8s = f8s.filter(val => {
        return val != ''
      })
      let f8s2 = [this.$attrs.value.mainAttachment]
      if (f8s2.length) {
        getFileList(f8s2).then(res => {
          debugger
          if (res.status) {
            try {
              that.fileList = res.data[0]
              // that.showFiles = res.data
              // setTimeout(() => {
              //   that.preViewFile(res.data[0])
              // }, 500)

            } catch (e) {
              that.$message({
                type: 'error',
                message: '系统异常！'
              })
            }
          }
        })
      }
      
      if (f8s3.length) {
        getFileList1(f8s3).then(res => {
          debugger
          if (res.status) {
            try {
              that.showFiles = res.data
              // let index= that.showFiles.index;
              // let subArr=res.data[index]
              // debugger
              // setTimeout(() => {
              //   that.preViewFile(res.data[index])
              // }, 500)

            } catch (e) {
              that.$message({
                type: 'error',
                message: '系统异常！'
              })
            }
          }
        })
      }
    },
    // filterFile (flag) {
    //   let file = []
    //   if (flag) {
    //     // 将fileList里与flag
    //     file = this.fileList.filter(val => {
    //       return val.groupToken == flag
    //     })
    //   } else {
    //     file = [...this.fileList]
    //   }
    //   return file
    // },
    // command (val) {
    //   switch (val) {
    //     case '1':
    //       this.fileFlag = '正文附件'
    //       this.showFiles = this.filterFile(
    //         this.current_article.contentattachmentid
    //       )
    //       break
    //     case '2':
    //       this.fileFlag = '图片源文件'
    //       this.showFiles = this.filterFile(this.current_article.uploadphotoid)
    //       break
    //     case '3':
    //       this.fileFlag = '其他附件'
    //       this.showFiles = this.filterFile(this.current_article.attachmentid)
    //       break
    //     default:
    //       this.fileFlag = '正文附件'
    //       this.showFiles = this.filterFile(
    //         this.current_article.contentattachmentid
    //       )
    //       break
    //   }
    // },
    download (file) {
      // 下载文件
      down(file.fileToken).then(res => {
        download(file, res)
      })
    },
    preViewFile (token) {
      // 切换到预览附件列表
      // debugger
      this.pre_view_file_control = true
      this.current_file = token
      // 给this的current_file字符串添加新的token
      // let token1 = this.$attrs.value.mainAttachment
      this.$set(this, 'current_file', token)
    },
    backToContent () {
      // 切换到正文内容预览
      this.$set(this, 'pre_view_file_control', false)
      // this.pre_view_file_control = false
    },
    backToList () {
      //切换到
      this.$router.push('/News/' + this.directory)
    },
    initData () {
      // this.getData()
      this.getFileList1() // 获取文件
    },
    getData () {
      // debugger
      // getDataByid(this.dataItem).then(res => {
        this.current_article = res.data.entityObject
      // this.getFileList()
      // })
    },
    fileMoseOver (e) {
      let el = e.target
      if (Array.from(el.classList).includes('file-item')) {
        el.lastElementChild.style.display = 'block'
        el.children[1].style.width = 'calc(90% - 52px)'
        e.stopPropagation()
      }
    },
    fileMoseLeave (e) {
      let el = e.target
      if (Array.from(el.classList).includes('file-item')) {
        el.lastElementChild.style.display = 'none'
        el.children[1].style.width = 'calc(90% - 24px)'
        e.stopPropagation()
      }
    },
    setArticleContent (content) {
      this.$refs.ARTICLE.append(content)
    }
  },
  watch: {
    id (val) {
      if (val) {
        this.initData()
      }
    }
  },
  mounted () {
    this.initData()
    document.documentElement.addEventListener('click', this.blurInput)
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy () {
    // localStorage.removeItem('CURRENT_ARTICLE')
    document.documentElement.removeEventListener('click', this.blurInput)
    window.removeEventListener('resize', this.resize)
  }
}
</script>

<style scoped lang="scss">
@import "asset/style/read";
</style>
