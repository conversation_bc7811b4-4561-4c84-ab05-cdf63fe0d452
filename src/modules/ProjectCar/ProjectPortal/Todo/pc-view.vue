<template>
  <CardFlow
    :cards="cards"
    :loading="loading"
    :finished="finished"
    left-description="已完成全部待办"
    @indexChange="handleChange"
    @loadMore="loadMore"
    ref="cardFlowRef"
  >
    <template v-slot:card="{ card }">
      <showcase v-bind="card" />
    </template>
    <template slot="panel">
      <div v-if="currentData" class="flex flex-column full-height">
        <header class="card-header">
          <div class="flex col-center m-b-24">
            <div class="card-title">{{ currentData.taskName }}</div>
            <div class="m-l-20">
              <card-tag v-for="(tag, index) in currentData.tags" :key="index" :tag="tag" />
              <card-tag
                v-if="showProjectName && currentData && currentData.projectName"
                style="margin-left: 8px"
                :key="currentData.projectId"
                :tag="{ color: '#3C83FF', text: currentData.projectName }"
              />
            </div>
          </div>
          <div class="card-desc">
            <span>{{ currentData.taskCreatorName }}</span>
            <fks-divider direction="vertical" />
            <span>{{ formatDate(currentData.processCreateDate) }}</span>
          </div>
        </header>
        <card-view
          :params="currentData"
          class="flex-grow-1 overflow-y-auto"
          @setFormData="$refs['cardFlowRef'].disableContentLoading()"
          @revocation="revocation"
          @refresh="handleRefresh"
          @modify="handleModify"
        />
        <revocation-drawer
          ref="revocationDrawer"
          :cur-car.sync="curCar"
          :is-cur="true"
          @refreshList="handleRefresh(true)"
        ></revocation-drawer>
      </div>
      <notification-confirm ref="notificationRef" />
    </template>
  </CardFlow>
</template>

<script>
import { getFormDetail, userTaskLists } from '@modules/Todo/api'
import dayjs from 'dayjs'
import statusTagMixin from '@/mixins/statusTagMixin'
import { mapGetters, mapMutations, mapState, mapActions } from 'vuex'
import * as MutationTypes from '@/store/Mutation/mutationTypes'
import * as StateTypes from '@/store/State/stateTypes'
import * as ActionTypes from '@/store/Action/actionTypes'
import OverflowTooltip from '@components/OverflowTooltip/index.vue'
import FormCenterDrawerView from '@modules/FormCenter/drawer-view.vue'
import CardFlow from '@components/CardFlow/index.vue'
import Showcase from '@components/CardFlow/components/showcase.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import FormCenter from '@modules/FormCenter/index.vue'
import CardView from '@modules/FormCenter/card-view.vue'
import { getFlowButton1 } from '@/api/carApply'
import RevocationDrawer from '@modules/CarApply/components/RevocationDrawer.vue'
import waitStatusUpdateMixin from '@/mixins/waitStatusUpdateMixin'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import * as GetterTypes from '@store/Getter/getterTypes'
import NotificationConfirm from '@components/NotificationConfirm/pc-view.vue'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'

export default {
  name: 'pc-view',
  components: {
    NotificationConfirm,
    RevocationDrawer,
    CardView,
    FormCenter,
    CardTag,
    Showcase,
    CardFlow,
    FormCenterDrawerView,
    OverflowTooltip,
  },
  mixins: [statusTagMixin, waitStatusUpdateMixin, globalSearchMixin, isConfirmXCMixin],
  data() {
    return {
      params: null,
      tableData: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      dueTableData: [],
      duePageNo: 1,
      duePageSize: 10,
      currentData: null,
      finished: false,
      curCar: null,
    }
  },
  computed: {
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    showProjectName() {
      // 是否展示项目名，处于公司门户-首页展示，项目门户-首页不展示
      return this.$route.name === 'Todo' || this.$route.name === 'companyTodo'
    },
    cards() {
      return this.tableData.map((item) => {
        const tags = [{ text: this.getTagText(item), color: this.getTagColor(item) }]
        if (item.flagBb) {
          tags.push({ text: '补报', color: '#FFA418' })
        }
        return {
          ...item,
          title: item.taskName,
          tags,
          descriptions: [
            { label: '项目名称', value: item.projectName },
            { label: '发起人', value: item.taskCreatorName },
            { label: '处理人', value: item.taskAsigneeName },
            { label: '开始时间', value: this.formatDate(item.processCreateDate) },
          ],
        }
      })
    },
  },
  methods: {
    ...mapMutations([MutationTypes.SET_PROJECT_INFO]),
    ...mapActions([ActionTypes.UPDATE_PROJECT_STATUS]),
    globalSearch() {
      this.tableData = []
      this.pageNo = 1
      this.getData(0).then(() => {
        if (this.tableData.length) {
          this.$refs.cardFlowRef.resetActiveIndex(0)
        }
      }) //传0表示重新开始查询
    },
    revocation() {
      const { bizId, ...rest } = this.currentData
      this.curCar = { ...rest, id: bizId }
      this.$nextTick(() => {
        this.$refs.revocationDrawer.open()
      })
    },
    handleModify(modifyKey) {
      const newCurrentData = JSON.parse(JSON.stringify(this.currentData))
      this.currentData = null
      this.$nextTick(() => {
        const buttonObj = modifyKey ? { buttonKey: modifyKey } : {}
        const { buttonKey, ...rest } = newCurrentData
        this.currentData = { ...rest, ...buttonObj }
      })
      if (modifyKey) {
        this.getNotification(modifyKey).then((res) => {
          if (res === false) {
            this.$refs.notificationRef.open({ buttonKey: modifyKey, checked: res })
          }
        })
      }
    },
    handleRefresh(trigger) {
      let activeIndex = this.$refs.cardFlowRef.activeIndex
      // trigger为true需要删除一条数据
      if (trigger) {
        this.tableData.splice(activeIndex, 1)
        // 删除数据后，index需要重置为0，默认选中项也要到第一项
        activeIndex = 0
        this.$refs.cardFlowRef.resetActiveIndex(0)
        this.pageNo = 0
      }
      this.fetchData(activeIndex).then(() => {
        const index = activeIndex <= this.cards.length - 1 ? activeIndex : 0
        const item = this.cards[index]
        if (item) {
          this.handleChange(item)
        } else {
          this.currentData = null
          this.$refs.cardFlowRef.resetActiveIndex(null)
        }
      })
    },
    loadMore() {
      this.getData(++this.pageNo)
    },
    async handleChange(row) {
      const { projectName, projectId } = row
      await this[ActionTypes.UPDATE_PROJECT_STATUS]({projectName, projectId})
      this[MutationTypes.SET_PROJECT_INFO]({ projectName, projectId })
      const params = {
        type: 'execute',
        formKey: row.formKey,
        taskKey: row.taskKey,
        taskId: row.taskId,
        bizId: row.formBizId,
        processInstanceId: row.processInstanceId,
        formName: row.formName,
        projectId,
        projectName,
      }
      this.currentData = null
      this.$nextTick(() => {
        let data = { ...row, ...params }
        this.$set(this.$data, 'currentData', data)
      })
    },
    handleClose(needRefresh) {
      this.params = null
      needRefresh && this.getData(1)
    },
    async fetchData(pageNo) {
      // 等待一些前置状态更新
      await this.waitStatusUpdate()

      // 1. 获取列表数据
      const res = await userTaskLists({
        ...this[GetterTypes.GET_GLOBAL_STATE],
        pageNo: pageNo + 1,
        pageSize: 1,
        columnName: 'createDate',
        sort: 'desc',
      })

      const { list = [] } = res.data || {}

      if (list.length) {
        const item = list[0]
        let ids = item.formBizId
        const buttonRes = await getFlowButton1(ids)
        const { data: formDetails } = await getFormDetail(ids)
        // 添加按钮参数
        if (buttonRes.data[item.formBizId] && buttonRes.data[item.formBizId].length) {
          item.buttonsSmall = buttonRes.data[item.formBizId].filter(
            (button) => button.buttonSizeType === 'small'
          )
        }

        // 之前是对整个 list 做 map
        let newItem = {
          ...item,
          projectName: formDetails[item.formBizId]?.projectName,
        }

        // 判断当前数据是否存在且 id 一致
        if (this.tableData[pageNo] && this.tableData[pageNo].formBizId === newItem.formBizId) {
          // 如果 id 相同，替换原数据
          this.tableData.splice(pageNo, 1, newItem)
        } else {
          // 如果 id 不一致，则删除当前页数据
          this.tableData.splice(pageNo, 1)
        }
        // this.tableData.splice(pageNo, 1, newItem);
      } else {
        this.tableData = []
      }
    },
    getData(pageNo, searchParams = {}) {
      if (!pageNo) {
        this.loading = true
      }
      return userTaskLists({
        ...this[GetterTypes.GET_GLOBAL_STATE],
        pageNo: pageNo || this.pageNo,
        pageSize: this.pageSize,
        columnName: 'createDate',
        sort: 'desc',
      })
        .then(async (res) => {
          if (res.status) {
            const { list, total, isLastPage } = res.data || []
            this.total = total
            this.finished = isLastPage
            const ids = list.map((item) => item.formBizId).join(',')
            const response = await getFlowButton1(ids)
            // 添加按钮参数
            list.forEach((item) => {
              if (response.data[item.formBizId] && response.data[item.formBizId].length) {
                item.buttonsSmall = response.data[item.formBizId].filter(
                  (button) => button.buttonSizeType === 'small'
                )
              }
            })
            if (ids && this.showProjectName) {
              const { data: formDetails } = await getFormDetail(ids)
              const newList = list.map((item) => {
                const projectName = formDetails[item.formBizId]?.projectName
                const projectId = formDetails[item.formBizId]?.projectId
                return {
                  ...item,
                  projectName,
                  projectId,
                }
              })
              this.tableData = [...this.tableData, ...newList]
            } else {
              this.tableData = [...this.tableData, ...list]
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    formatDate(str) {
      return dayjs(str).format('YYYY-MM-DD HH:mm')
    },
  },
  created() {
    this.getData()
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/headers';
</style>
