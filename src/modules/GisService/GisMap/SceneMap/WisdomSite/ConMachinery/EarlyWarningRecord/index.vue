<!--
* @Des: 
* @Author: <EMAIL>
* @Date: 0000-00-00 00:00:00
* @LastEditor: <EMAIL>
* @LastEditTime: 0000-00-00 00:00:00
-->
<template>
  <div class="content">
    <fks-query-page
      ref="query"
      :data="tableData"
      @query="initData"
      :total="total"
      v-loading="loading"
      :pageSize.sync="pageSize"
      :currentPage.sync="currentPage"
      :hideSwitch.sync="hideSwitch"
      tableName="机械设备预警列表"
      :page-sizes="[15, 20, 50, 100]"
      style="min-height:500px;height:100%"
      layout="total, sizes, prev, pager, next, jumper"
      @clear="clearQueryData"   
    >
      <template slot="search">
        <fks-search-item label="子工程:" :span="6">
          <trunk-select v-model="trunkVal" :disabled="this.$store.state.portal.type !== 1"></trunk-select>
        </fks-search-item>
        <fks-search-item label="单元工程:" :span="6">
          <unit-pro-select v-model="unitkVal" :trunkId="trunkVal"></unit-pro-select>
        </fks-search-item>
        <fks-search-item label="设备类型:" :span="6">
          <fks-select v-model="equipmentType" placeholder="请选择">
            <fks-option
              v-for="item in equipmentList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </fks-option>
          </fks-select>
        </fks-search-item>
        <fks-search-item label="设备名称:" :span="6">
          <fks-input v-model="equipmentName"></fks-input>
        </fks-search-item>
      </template>
      <template>
        <fks-table-column type="index" label="#" align="center" width="50"></fks-table-column>
        <fks-table-column label="时间" align="center" width="150" prop="warnTime"></fks-table-column>
        <fks-table-column label="子工程" align="center" width="150" prop="parentNode"></fks-table-column>
        <fks-table-column label="单元工程" align="center" width="150" prop="node"></fks-table-column>
        <fks-table-column label="设备名称" align="center" width="150" prop="machineName"></fks-table-column>
        <fks-table-column label="设备类型" align="center" width="150" prop="machineType"></fks-table-column>
        <fks-table-column label="预警内容" align="center" min-width="300" prop="warnContent"></fks-table-column>
      </template>
    </fks-query-page>
  </div>
</template>

<script>
import Mix from '@/mixins/module'
import trunkSelect from '../../Components/trunkSelect'
import unitProSelect from '../../Components/unitProSelect'
import { getMachineWarn } from './api'
export default {
  name: 'EarlyWarningRecord',
  mixins: [Mix],
  // 部件
  components: {
    trunkSelect,
    unitProSelect
  },
  // 静态
  props: {
  },
  data: function() {
    return {
      tableData: [],
      total: 0,
      loading: false,
      pageSize: 15,
      currentPage: 1,
      hideSwitch: true,
      trunkVal: this.$store.state.portal.type == 1 ? "" : this.$store.state.portal.id,
      unitkVal: "",
      equipmentType: "",
      equipmentList: [
        {value: '塔吊', label: '塔吊'},
        {value: '挖掘机', label: '挖掘机'},
      ],
      equipmentName: "",
    }
  },
  // 对象内部的属性监听，也叫深度监听
  watch: {
  },
  // 属性的结果会被缓存，除非依赖的响应式属性变化才会重新计算。主要当作属性来使用；
  computed: { 
  },
  // 方法表示一个具体的操作，主要书写业务逻辑；
  methods: {
    async initData() {
      var params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        name: this.equipmentName,
        node: this.unitkVal,
        parentNode: this.trunkVal,
        type: this.equipmentType
      }
      let res = await getMachineWarn(params)
      if(res.status) {
        this.tableData = res.data.list
        this.total = res.data.total
      }
    },
    clearQueryData() {
      this.currentPage = 1;
      this.pageSize = 15;
      this.equipmentName = '';
      this.trunkVal = this.$store.state.portal.type == 1 ? "" : this.$store.state.portal.id;
      this.unitVal = '';
      this.equipmentType = ''
      this.getData()
    }
  },
  // 请求数据
  created() {
  },
  mounted() {
    this.initData()
  }
}
</script>

<style lang="scss" scoped>
.content{
  width: 100%;
  height: 100%;
  padding: 0px 0px;
  padding-top: 0px;
  background: #fff;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-size: 16px;
}
</style>
