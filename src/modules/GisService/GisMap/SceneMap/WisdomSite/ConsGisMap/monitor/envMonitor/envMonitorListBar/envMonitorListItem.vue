<template>
  <div class="collection-block" @click="$emit('click')">
    <fks-row
      type="flex"
      justify="space-between"
      align="center"
      class="mb-1"
    >
      <fks-col
        :span="20"
        class="text-truncate d-flex align-items-center"
      >
        <img
          src="@/modules/GisService/rx-app/src/assets/image/monitor/environment.png"
          width="20"
          height="20"
          alt="泵站"
          class="mr-1"
        >
        <span class="text-16 font-weight-bold">{{ item.codeName }}</span>
      </fks-col>
      <!-- <fks-col :span="4" class="text-right">
        <i
          :class="`${item.isCollect ? 'fks-icon-star-on' : 'fks-icon-star-off'} text-18 text-warning mr-2`"
          @click.stop="collectOrRemove(item.codeId, item.isCollect)"
        />
      </fks-col> -->
    </fks-row>
    <fks-row :gutter="2" class="collection-block-text">
      <fks-col
        :span="8"
        v-for="field in fieldList"
        :key="field.name"
        class="text-truncate"
      >
        {{ field.name }}:
        &nbsp;
        {{ item[field.name] === undefined || item[field.name] === null ? '-' : item[field.name] }}
        &nbsp;
        {{ field.unit }}
      </fks-col>
    </fks-row>
  </div>
</template>

<script>
  import envMonitorListItemMixin from '../../monitorListItemMixin';
  import { fieldList } from '../constant';

  export default {
    mixins: [envMonitorListItemMixin],
    props: {
      item: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        fieldList
      };
    }
  };
</script>
