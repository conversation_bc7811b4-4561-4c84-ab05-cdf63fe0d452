<template>
  <div
    :id="chartsId" class="full-width full-height"
  ></div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  name: 'mline',
  props: {
    id: {
      default: 'date'
    },
    time: {
      type: Array,
      default: function () {
        return [];
      }
    },
    timeType: {
      type: String | Array
    },
    inWaterData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    outWaterData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dataTheme: {
      type: String,
      default: function () {
        return 'light-theme';
      }
    }
  },
  computed: {
    chartsId(val) {
      return this.id;
    },
    textColor() {
      return this.dataTheme === 'light-theme' ? '#555' : '#eee';
    }
  },
  watch: {
    time: {
      handler(newV, oldV) {
        this.initChart();
      },
      deep: true
    },
    dataTheme: {
      handler(newV, oldV) {
        this.initChart();
      },
      deep: true
    },
    timeType: {
      handler(newV, oldV) {
        this.initChart();
      },
      deep: true
    }
  },
  data() {
    return {
      quarterTime: ['第一季度', '第二季度', '第三季度', '第四季度']
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let myChart = echarts.init(document.getElementById(this.chartsId));
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
          position: function (pt) {
            return [pt[0], '10%'];
          },
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['进水量', '供水量'],
          width: '75%',
          itemWidth: 35,
          itemGap: 14
        },
        dataZoom: this.id === 'tenDays' ? [
          {
            type: 'inside',
            start: 0,
            end: 20
          },
          {
            start: 0,
            end: 20
          }
        ] : [],
        xAxis: {
          type: 'category',
          data: this.id === 'quarter' ? this.quarterTime : this.time,
          boundaryGap: true,
          axisLabel: {
            color: this.textColor,
            fontSize: 10,
            padding: [14, 0, 0, 0],
            margin: 10,
            interval: 0,
            rotate: -40
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#CCCCCC'
            }
          }
        },
        yAxis: [
          {
            minInterval: 1,
            show: true,
            name: '水量(m³)',
            nameTextStyle: {
              color: this.textColor
            },
            type: 'value',
            scale: true,
            min: 0,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#CCCCCC'
              }
            },
            axisTick: { show: false },
            axisLabel: {
              color: this.textColor,
              fontSize: 12
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#EEEEEE'
              }
            }
          }
        ],
        grid: {
          top: '10%',
          left: '5%',
          right: this.id === 'tenDays' ? '12%' : '5%',
          bottom: '3%',
          height: '265px',
          containLabel: true
        },
        series: [
          {
            name: '进水量',
            type: 'line',
            symbol: 'none',
            data: this.inWaterData,
            color: '#1890FF'
          },
          {
            name: '供水量',
            type: 'line',
            symbol: 'none',
            data: this.outWaterData,
            color: '#FFCB27'
          }
        ]
      });
    }
  }
};
</script>
