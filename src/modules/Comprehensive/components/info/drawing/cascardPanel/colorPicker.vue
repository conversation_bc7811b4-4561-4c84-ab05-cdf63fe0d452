<template>
  <article
    style="display:flex;align-items:center width:100px"
    class="project-color-picker"
  >
    <fks-color-picker
      v-model="color"
      size="mini"
      style="width:100%"
      @change="changeColor"
      show-alpha
      :predefine="predefineColors"
    ></fks-color-picker>
  </article>
</template>
<script>
export default {
  props: {
    fill: String,
    type: String,
    line: Object
  },
  data() {
    return {
      dictionary: {
        point: 'fill',
        text: 'fill',
        line: 'color'
      },
      color: this.fill || this.line.stroke,
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ]
    };
  },
  methods: {
    changeColor() {
      const key = this.dictionary[this.type];
      this.$emit('onClick', { [key]: this.color });
    }
  }
};
</script>
<style lang="scss">
.project-color-picker {
  .fks-color-picker--mini .fks-color-picker__trigger {
    width: 100% !important;
  }
}
</style>
