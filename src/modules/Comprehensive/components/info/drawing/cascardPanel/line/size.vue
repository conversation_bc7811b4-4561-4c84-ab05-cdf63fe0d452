<template>
  <article>
    <div
      v-for="(size, index) in sizeArr"
      :key="index"
      class="svg"
      :type="`bold_${size / 4}`"
      style="display:flex;flex-wrap:nowrap;"
      @click="handleClick(size)"
    >
      <div style="min-width:40px;">{{ size / 4 }} {{$t('comprehensive.drawing.pound')}}</div>
      <svg-icon
        :iconColor="highLight(size)"
        :width="svg.width"
        :height="svg.height"
        iconName="普通直线"
      >
        <line
          :x1="0"
          :y1="svg.height / 2"
          :x2="svg.width"
          :y2="svg.height / 2"
          :stroke-width="size / 2"
        />
      </svg-icon>
    </div>
  </article>
</template>
<script>
import SvgIcon from '../../svg';
import { sizeArr } from './lineConfig';
export default {
  props: ['line'],
  name: 'point_style',
  data() {
    return {
      sizeArr: sizeArr,
      svg: {
        height: 30,
        width: 80
      },
      fontHighLight: 'color:#1890FF;font-weight:bold'
    };
  },
  computed: {
    dictionary() {
      return this.sizeArr.reduce(
        (dic, curSize) => ({
          ...dic,
          [`bold_${curSize / 4}`]: { strokeWidth: curSize }
        }),
        {}
      );
    }
  },
  components: {
    SvgIcon
  },
  methods: {
    handleClick(size) {
      const getShapeConfig = (size, dictionary) =>
        dictionary[`bold_${size / 4}`] || {};
      this.$emit('onClick', getShapeConfig(size, this.dictionary));
    },
    highLight(value) {
      return this.line.strokeWidth === value ? '#006ef6' : '#606266';
    }
  }
};
</script>

<style lang="scss" scoped>
.highLight {
  color: #1890FF;
  font-weight: bold;
}
</style>
