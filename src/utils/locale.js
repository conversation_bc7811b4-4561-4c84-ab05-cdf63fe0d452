/*
 * @Author: <EMAIL>
 * @Date: 2020-11-18 19:16:07
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2020-11-23 10:24:09
 * @Description: file content
 */
import storage from './storage'
import Vue from 'vue'
const loadedLanguages = ['zh-CN'] // 我们的预装默认语言

function setI18nLanguage(lang) {
  Vue.config.lang = lang
  storage.set('lang_config', lang)
  return lang
}

export function loadLanguageAsync(lang) {
  if (Vue.config.lang !== lang) {
    if (!loadedLanguages.includes(lang)) {
      return import(/* webpackChunkName: "lang-[request]" */ `fawkes-lib/src/locale/lang/${lang}`).then(msgs => {
        Vue.locale(lang, msgs.default)
        loadedLanguages.push(lang)
        return setI18nLanguage(lang)
      })
    }
    return Promise.resolve(setI18nLanguage(lang))
  }
  return Promise.resolve(lang)
}


/**
 * @description: 获取本地语言设置 当未配置时，选择浏览器默认语言
 * @param {type} 
 * @return: 
 */
export function getLangConfig() {
  let language = storage.get('lang_config') || storage.get('default_lang')
  if (language) return language
  if (navigator.appName == 'Netscape') {
    language = navigator.language || navigator.userLanguage
  }
  else {
    language = navigator.browserLanguage || navigator.userLanguage
  }
  if (language.indexOf('en') > -1) {
    language = "en"
  }
  else if (language.indexOf('zh') > -1) {
    language = "zh-CN"
  }
  else {
    language = "en"
  }
  return language
}