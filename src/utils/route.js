import { setRoutes } from '@/router';
import { getRouter } from '@/api/user';
import { transTree, uuid, isJSON } from '@/utils/util';
import store from '@/store';
import { subPrefix } from '@/settings';

/**
 * @description: 加载本地路由
 * @param {type}
 * @return {type}
 */
export const getLocalRoutes = async () => {
  let routes = [];
  const reqs = require.context('@/modules', true, /route\.js$/, 'lazy');
  let analysis = async (req, p) => {
    let reg = new RegExp('^' + p.replace('/route.js', '') + '\/[a-zA-Z]+\/route\.js$');
    for (let k = 0; k < reqs.keys().length; k++) {
      let dpath = reqs.keys()[k];
      if (reg.test(dpath)) {
        let getModule = () => import(`@/modules/${dpath.replace('./', '')}`);
        let cReq = await getModule();
        cReq = Object.assign(cReq.default, { meta: { menuId: uuid(18) } });
        req.children ? req.children.push(cReq) : req.children = [cReq];
        await analysis(cReq, dpath);
      }
    }
  };
  for (let i = 0; i < reqs.keys().length; i++) {
    let path = reqs.keys()[i];
    if (/^\.\/[a-zA-Z]+\/route\.js$/.test(path)) {
      let getModule = () => import(`@/modules/${path.replace('./', '')}`);
      let req = await getModule();
      req = Object.assign(req.default, { meta: { menuId: uuid(18) } });
      await analysis(req, path);
      path.includes('Home') ? routes.unshift(req) : routes.push(req);
    }
  }
  routes.push({ path: '*', redirect: '/404', hidden: true });
  store.commit('SET_ROUTES', routes);
  setRoutes(routes);
};

/**
 * @description: 加载线上路由
 * @param {type}
 * @return {type}
 */
export const getUserRoutes = () => {
  return new Promise((resolve, reject) => {
    let routes = [];
    getRouter().then(res => {
      if (res.status && res.data) {
        store.commit('RELOAD_ROUTE', false);
        const tree = [];
        for (let i = 0; i < res.data.length; i++) {
          try {
            const menuItem = { ...res.data[i] };
            menuItem.component = () => import(`@/modules${res.data[i].component}`);
            menuItem.name = res.data[i].code;
            menuItem.hidden = !!res.data[i].hidden;
            menuItem.path || (menuItem.path = '');
            menuItem.meta = (res.data[i] && isJSON(res.data[i].meta) && JSON.parse(res.data[i].meta)) || {};
            menuItem.meta.menuId = res.data[i].id;
            tree.push(menuItem);
          } finally {
            continue;
          }
        }
        tree.sort((v1, v2) => {
          return v1.sort === null ? v2.sort + 1 : (v2.sort === null ? -v1.sort - 1 : v1.sort - v2.sort);
        });
        let treeData = transTree(tree, 'id', 'parentId').val; // 解析为带children树结构的格式
        treeData = treeData.filter(td => {
          return !td.pathId || td.pathId.match(/\./g).length === 4;
        });
        routes = routes.concat(treeData);
      }
      routes.push({ path: '*', redirect: '/404', hidden: true });
      store.commit('SET_ROUTES', routes);
      setRoutes(routes);
      resolve(true);
    }).catch((e) => {
      console.info('🚀🚀', 'e -->', e, `<-- route.js/`);
      // setRoutes([{ path: '*', redirect: '/404', hidden: true }]);
      resolve(true);
    });
  });
};
