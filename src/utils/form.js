/*
 * @Author: <EMAIL>
 * @Date: 2020-06-24 16:46:45
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2020-07-24 10:28:10
 * @Description: file content
 */
import store from "@/store"
import * as types from '@/store/Getter/getterTypes'

export function setUserData(data) {
  // data[types.USER_NAME] = store.getters[types.USER_NAME]
  data[types.USER_FULLNAME] = store.getters[types.USER_FULLNAME]
  data[types.PRJ_DEP_CODE] = store.getters[types.PRJ_DEP_CODE]
  data[types.PRJ_DEP_NAME] = store.getters[types.PRJ_DEP_NAME]
  // data[types.USER_TELPHONE_NUM] = store.getters[types.USER_TELPHONE_NUM]
  // if (!data.prjName) {
  //   data.portal= store.state.portal.id
  //   // data.prjName = store.state.portal.name
  // }
}

export function isCustomFlow(data, prefix) {
  let flag = false
  if (data) {
    //判断是否以存在自由流程prefix
    if (data.match(new RegExp(`^${prefix}.*$`))) {
      flag = true
    }
  }
  return flag
}
