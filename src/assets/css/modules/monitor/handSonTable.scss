.monitor-table-style {
  .handsontable th,
  .handsontable td {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    text-align: center;
    min-width: 90px;
    border-color: #eee !important;
    .relative {
      padding: 0;
    }
  }
  .handsontable th {
    background-color: #f9f9f9;
    color: #191919;
  }
  .handsontable td {
    color: #393e45;;
  }
  .handsontable textarea {
    line-height: 30px;
    text-align: center;
  }
  // Handsontable Header
  .handsontable .handsontable.ht_clone_top .wtHider {
    padding-bottom: 2px;
  }
  // Handsontable DropdownMenu
  .handsontable .changeType {
    margin-top: 16px;
    margin-right: 4px;
  }
  // Handsontable左上角文字（末尾自带了一个空格）
  .handsontable.ht_clone_top_left_corner .colHeader.cornerHeader::before {
    content: '#';
  }
}

.attribute-table {
  .handsontable th,
  .handsontable td {
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    text-align: center;
    min-width: 90px;
    border-color: #eee !important;
    .relative {
      padding: 0;
    }
  }
  .handsontable th {
    background-color: #e8f4ff;
    color: #191919;
  }
  .handsontable td {
    color: #393e45;;
  }
  .handsontable textarea {
    line-height: 48px;
    text-align: center;
  }
  // Handsontable Header
  .handsontable .handsontable.ht_clone_top .wtHider {
    padding-bottom: 2px;
  }
  // Handsontable DropdownMenu
  .handsontable .changeType {
    margin-top: 16px;
    margin-right: 4px;
  }
  // Handsontable左上角文字（末尾自带了一个空格）
  .handsontable.ht_clone_top_left_corner .colHeader.cornerHeader::before {
    content: '#';
  }
  .handsontable tbody th.ht__highlight, .handsontable thead th.ht__highlight {
    background-color: #e8f4ff;
  }
}

// element-ui 表格样式同步
.single-extend {
  .el-table--border {
    border-color: #eee;
  }
  .el-table--border tr {
    th {
      border-right: 1px solid #eee;
    }
    th.is-leaf {
      border-color: #eee;
    }
    td {
      border-right: none
    }
    td:first-child {
      background: #f8f8f8;
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      font-weight: bold;
    }
  }
}

// 解决中文下日期text样式问题
.htDatepickerHolder .pika-table th abbr {
  text-decoration: blink !important;
}
