$default-theme: (
    base-color: #fff,
    background-color: rgba(9, 24, 44, 0.8),
    border-color: rgba(21, 36, 56, 1),
    font-color: #ffffff,
    img-filter: 0,
    table-title-color: rgba(0, 80, 197, 0.23),
    table-content-color: rgba(0, 80, 197, 0.16),
    history-btn-bg-color: #003e83,
    history-btn-color: #32c5ff,
    radio-bg-color: rgba(9, 24, 44, 0.8),
    danger-status-color: #eeeeee,
);
// 浅色背景暂未确定，目前暂定如下样式
$light-theme: (
    base-color: rgb(0, 0, 0),
    background-color: #d8d8d8,
    border-color: #979797,
    font-color: #555555,
    img-filter: 1,
    table-title-color: rgba(0, 80, 197, 0.78),
    table-content-color: rgba(0, 80, 197, 0.55),
    history-btn-bg-color: #32c5ff,
    history-btn-color: #ffffff,
    radio-bg-color: #ffffff,
    danger-status-color: #555555,
);
//创建映射
$themes: (
    default-theme: $default-theme,
    light-theme: $light-theme,
);
