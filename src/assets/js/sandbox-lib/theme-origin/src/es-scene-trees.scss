@import "common/var.scss";

.es-scene-tree {
    position: relative;
    .es-scene-tree-panel {
        position: absolute;
        left: 46px;
        top: 0;
        width: 212px;
        padding: 8px;
        box-sizing: border-box;
        border: 1px solid $--color-border-theme;
        background: $--color-background-theme;
        border-radius: 4px;
        min-height: 300px;
        max-height: 60%;
        overflow-y: auto;
        .es-scene-tree-title {
            height: 40px;
            left: 40px;
            font-size: 16px;
            color: $--color-text-highlight;
            text-align: left;
            padding: 8px 16px;
            box-sizing: border-box;
        }
        .fks-tree-node:focus,
        .fks-tree-node__content:focus,
        .fks-tree,
        .fks-tree-node,
        .fks-tree-node__content{
            background: transparent !important;
            color: $--color-text-header;
        }
        .fks-tree-node__content:hover,
        .fks-tree-node:hover,
        .fks-tree-node.is-checked .fks-tree-node__content{
            background: transparent;
            color: $--color-text-highlight;
        }
        .fks-tree--highlight-current .fks-tree-node.is-current > .fks-tree-node__content {
            background-color: transparent;
        }
    }
}
.slide-expand-leave-active,
.slide-expand-enter-active {
    transition: all .3s ease
}
.slide-expand-leave-to,
.slide-expand-enter {
    -webkit-transform-origin: 0 100% 0;
    transform-origin: 0 100% 0;
    -webkit-transform: translateX(-50px);
    transform: translateX(-50px);
    opacity: 0;
}
.slide-expand-leave {
    -webkit-transform-origin: 0 100% 0;
    transform-origin: 0 100% 0;
    -webkit-transform: scale(1);
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
}