/*
 * @Author: <EMAIL>
 * @Date: 2020-09-17 09:36:15
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2020-10-14 08:36:14
 * @Description: 消息相关接口
 */
import request from '@/utils/request';

/**
 * @description: 根据用户ID和name获得所有消息
 */
export function getMsgByIdAndName(params) {
  return request({
    url: '/sys-msg/socketMsg/page',
    method: 'get',
    params
  });
}

/**
 * @description: 根据信息ID更新状态
 * @param   status 状态(0:已读,-1:未读,-2:删除)
 * @param   receiveLogId 站内信接收记录id
 */
export function updateMsgStatusById(data) {
  return request({
    url: '/sys-msg/socketMsg/updateStatus',
    method: 'POST',
    params: {
      receiveLogIdList: data.receiveLogIdList,
      status: data.status
    }
  });
}
