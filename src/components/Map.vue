<template>
  <div>
    <div id="map"></div>
    <div class="controls">
      <h3>Yuan Route</h3>
      <button @click="toggleLayer('yuan', 'line')">Line</button>
      <button @click="toggleLayer('yuan', 'point')">Point</button>
      <h3>Xiao Route</h3>
      <button @click="toggleLayer('xiao', 'line')">Line</button>
      <button @click="toggleLayer('xiao', 'point')">Point</button>
    </div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import yuanData from '@/data/yuan-coordinate.json';
import xiaoData from '@/data/xiao-coordinate.json';
import HdecTransform from '@/utils/hdecTransform';

export default {
  name: 'CustomMap',
  data() {
    return {
      map: null,
    };
  },
  mounted() {
    mapboxgl.accessToken = process.env.VUE_APP_MAPBOX_TOKEN;
    this.map = new mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mapbox/streets-v11',
      center: [106.2925, 29.393],
      zoom: 12,
    });

    this.map.on('load', () => {
      this.map.addSource('tianditu-img', {
        type: 'raster',
        tiles: [
          `http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${process.env.VUE_APP_TIANDITU_TOKEN}`,
        ],
        tileSize: 256,
      });
      this.map.addLayer({
        id: 'tianditu-img-layer',
        type: 'raster',
        source: 'tianditu-img',
        minzoom: 0,
        maxzoom: 18,
      });

      const transformedYuanCoordinates = yuanData.map(item => {
        const [lon, lat] = HdecTransform.gcj02Twgs84(item.longitude, item.latitude);
        return [lon, lat];
      });

      const transformedXiaoCoordinates = xiaoData.map(item => {
        const [lon, lat] = HdecTransform.gcj02Twgs84(item.longitude, item.latitude);
        return [lon, lat];
      });

      // Calculate bounds for both routes
      const bounds = new mapboxgl.LngLatBounds();
      transformedYuanCoordinates.forEach(coord => {
        bounds.extend(coord);
      });
      transformedXiaoCoordinates.forEach(coord => {
        bounds.extend(coord);
      });

      this.map.fitBounds(bounds, {
        padding: 50,
        maxZoom: 15,
      });

      // Yuan Route Sources and Layers
      const yuanLineSource = {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: transformedYuanCoordinates,
          },
        },
      };

      const yuanPointSource = {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: transformedYuanCoordinates.map((coord, index) => ({
            type: 'Feature',
            properties: { index: index + 1 },
            geometry: {
              type: 'Point',
              coordinates: coord,
            },
          })),
        },
      };

      this.map.addSource('yuan-route-line-source', yuanLineSource);
      this.map.addSource('yuan-route-point-source', yuanPointSource);

      this.map.addLayer({
        id: 'yuan-route-line',
        type: 'line',
        source: 'yuan-route-line-source',
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
        },
        paint: {
          'line-color': '#00FFFF',
          'line-width': 8,
        },
      });

      this.map.addLayer({
        id: 'yuan-route-point',
        type: 'circle',
        source: 'yuan-route-point-source',
        paint: {
          'circle-radius': 8,
          'circle-color': '#FF0000',
        },
        layout: {
          visibility: 'none',
        },
      });

      this.map.addLayer({
        id: 'yuan-route-point-labels',
        type: 'symbol',
        source: 'yuan-route-point-source',
        layout: {
          'text-field': ['get', 'index'],
          'text-variable-anchor': ['top', 'bottom', 'left', 'right'],
          'text-radial-offset': 0.5,
          'text-justify': 'auto',
          'icon-image': ['concat', ['get', 'icon'], '-15'],
          visibility: 'none',
        },
        paint: {
          'text-color': '#FFFFFF',
        },
      });

      // Xiao Route Sources and Layers
      const xiaoLineSource = {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: transformedXiaoCoordinates,
          },
        },
      };

      const xiaoPointSource = {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: transformedXiaoCoordinates.map((coord, index) => ({
            type: 'Feature',
            properties: { index: index + 1 },
            geometry: {
              type: 'Point',
              coordinates: coord,
            },
          })),
        },
      };

      this.map.addSource('xiao-route-line-source', xiaoLineSource);
      this.map.addSource('xiao-route-point-source', xiaoPointSource);

      this.map.addLayer({
        id: 'xiao-route-line',
        type: 'line',
        source: 'xiao-route-line-source',
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
        },
        paint: {
          'line-color': '#00FF00',
          'line-width': 8,
        },
      });

      this.map.addLayer({
        id: 'xiao-route-point',
        type: 'circle',
        source: 'xiao-route-point-source',
        paint: {
          'circle-radius': 8,
          'circle-color': '#0000FF',
        },
        layout: {
          visibility: 'none',
        },
      });

      this.map.addLayer({
        id: 'xiao-route-point-labels',
        type: 'symbol',
        source: 'xiao-route-point-source',
        layout: {
          'text-field': ['get', 'index'],
          'text-variable-anchor': ['top', 'bottom', 'left', 'right'],
          'text-radial-offset': 0.5,
          'text-justify': 'auto',
          'icon-image': ['concat', ['get', 'icon'], '-15'],
          visibility: 'none',
        },
        paint: {
          'text-color': '#FFFFFF',
        },
      });
    });
  },
  methods: {
    toggleLayer(routeId, layerType) {
      if (!this.map) return;

      const lineLayerId = `${routeId}-route-line`;
      const pointLayerId = `${routeId}-route-point`;
      const pointLabelsLayerId = `${routeId}-route-point-labels`;

      // Hide all layers for both routes first
      this.map.setLayoutProperty('yuan-route-line', 'visibility', 'none');
      this.map.setLayoutProperty('yuan-route-point', 'visibility', 'none');
      this.map.setLayoutProperty('yuan-route-point-labels', 'visibility', 'none');
      this.map.setLayoutProperty('xiao-route-line', 'visibility', 'none');
      this.map.setLayoutProperty('xiao-route-point', 'visibility', 'none');
      this.map.setLayoutProperty('xiao-route-point-labels', 'visibility', 'none');

      // Show selected layer
      if (layerType === 'line') {
        this.map.setLayoutProperty(lineLayerId, 'visibility', 'visible');
      } else if (layerType === 'point') {
        this.map.setLayoutProperty(pointLayerId, 'visibility', 'visible');
        this.map.setLayoutProperty(pointLabelsLayerId, 'visibility', 'visible');
      }
    },
  },
};
</script>

<style>
#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}
.controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
}
</style>
