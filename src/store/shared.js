import { MICRO_DAM_URL, WATCH_URL, ATTENDANCE } from '@/plugins/constants/path.js';
import request from '@/utils/request';
import store from '@/store';
const axios = require('axios');

const state = () => ({
  updateList: 0,
  btnPermissions: [],
  axiosCancelArr: [],
  fileList: [],
  pageSize: 10,
  currentDamId: '',
  currentDamInfo: {
    damId: '',
    name: '',
    stationId: 4000,
    stationName: ''
  },
  stationId: 4000,
  currentProId: null,
  damsInfo: [],
  filterDams: [],
  damInfo: {},
  breadcrumb: [],
  damList: [],
  showTab: true,
  showDamSelect: false,
  isSingleDam: false,
  HANDSONTABLE_I18N: {
    'zh-CN': {
      previousMonth: '上一月',
      nextMonth: '下一月',
      months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      weekdaysShort: ['日', '一', '二', '三', '四', '五', '六']
    },
    en: {
      previousMonth: 'Previous Month',
      nextMonth: 'Next Month',
      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      weekdays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      weekdaysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    },
    en_front: {
      previousMonth: 'Previous Month',
      nextMonth: 'Next Month',
      months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      weekdays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      weekdaysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    },
    pt: {
      previousMonth: 'Mês passado',
      nextMonth: 'Mês que VEM',
      months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
      weekdays: ['Dia', 'Um', 'Dois', 'Três', 'Quatro', 'Cinco', 'Seis'],
      weekdaysShort: ['Dia', 'Um', 'Dois', 'Três', 'Quatro', 'Cinco', 'Seis']
    },
    fr: {
      previousMonth: 'Janvier dernier',
      nextMonth: 'Mois prochain',
      months: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
      weekdays: ['Jour', 'Un', 'Deux', 'Trois', 'Quatre', 'Cinq', 'Six'],
      weekdaysShort: ['Jour', 'Un', 'Deux', 'Trois', 'Quatre', 'Cinq', 'Six']
    },
    es: {
      previousMonth: 'El mes pasado',
      nextMonth: 'El próximo mes',
      months: ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'],
      weekdays: ['Día', 'Uno', 'II', 'Iii', 'Iv', 'V', 'Vi'],
      weekdaysShort: ['Día', 'Uno', 'II', 'Iii', 'Iv', 'V', 'Vi']
    },
    lo: {
      previousMonth: 'ປົກຕັ້ງ',
      nextMonth: 'ທຸກຕິດຕັ້ງ',
      months: ['ມີນກອນ', 'ເມສາ', 'ມີນນາ', 'ເມສາ', 'ພຶດສະດ', 'ມິນນາ', 'ພາຣະກດ', 'ສິງຫາ', 'ຕິດຕັ້ງ', 'ຕິດຕັ້ງ', 'ໃນເດືອນເມສາ', 'ໃນເດືອນທັນວາ'],
      weekdays: ['ວັນນີ້', 'ຫນຶ່ງ', 'ເພື່ມ', '3', 'ສີ່', 'ຟີມ', '6'],
      weekdaysShort: ['ວັນນີ້', 'ຫນຶ່ງ', 'ເພື່ມ', '3', 'ສີ່', 'ຟີມ', '6']
    }
  },
  isEnvironment: false,
  engineerType: 1,
  environmentType: 1,
  projectId: null,
  isRoot: false,
  currentPosition: null
});

const getters = {
  isInStation: state => {
    return state.engineerType === 1;
  },
  stationOrDamParams: function (state, getters, rootState) {
    let defaultParam = {
      singParam: {}, // 电站Id / 大坝id,
      params: {} // 大坝下包括电站Id 电站下不包括damId
    };
    defaultParam.singParam[`${getters.isInStation ? 'stationId' : 'damId'}`] = getters.isInStation
      ? rootState.monitor.stationId
      : rootState.monitor.damId;
    defaultParam.params = Object.assign(defaultParam.params, state.currentDamInfo);
    return defaultParam;
  }
};

const mutations = {
  SET_IS_SINGLE_DAM(state, isSingleDam) {
    state.isSingleDam = isSingleDam;
  },
  SET_BTN_PERMISSIONS(state, btnPermissions) {
    state.btnPermissions = btnPermissions;
  },
  PUSH_CANCEL(state, cancel) {
    state.axiosCancelArr.push(cancel.cancelToken);
  },
  CLEAR_CANCEL(state) {
    state.axiosCancelArr && state.axiosCancelArr.forEach(e => {
      e();
    });
    state.axiosCancelArr = [];
  },
  SET_CURRENT_PROJECT_ID(state, projectId) {
    state.projectId = projectId;
  },
  SET_ENGINEER_TYPE(state, engineerType) {
    state.engineerType = engineerType;
  },
  SET_CURRENT_DAM_ID(state, currentDamId) {
    state.currentDamId = currentDamId;
  },
  SET_CURRENT_DAM_INFO(state, currentDamInfo) {
    state.currentDamInfo = currentDamInfo;
    state.stationId = currentDamInfo.stationId;
  },
  SET_CURRENT_DAM_NAME(state, damName) {
    state.damName = damName;
  },
  SET_CURRENT_PRO_ID(state, currentProId) {
    state.currentProId = currentProId;
  },
  SET_FILE_LIST(state, fileList) {
    state.fileList = fileList;
  },
  SET_PAGE_SIZE(state, pageSize) {
    state.pageSize = pageSize;
  },
  SET_DAM_INFO(state, damInfo) {
    state.damInfo = damInfo;
  },
  SET_DAM_LIST(state, damList) {
    state.damList = damList;
  },
  SET_BREADCRUMB(state, para) {
    state.breadcrumb = para;
  },
  SET_SHOWTAB(state, para) {
    state.showTab = para;
  },
  SET_SHOW_DAM_SELECT(state, showDamSelect) {
    state.showDamSelect = showDamSelect;
  },
  SET_IS_ENVIRONMENT(state, value) {
    state.isEnvironment = value;
  },
  SET_ENVIRONMENT_TYPE(state, environmentType) {
    state.environmentType = environmentType;
  },
  SET_DAMS_INFO(state, value) {
    state.damsInfo = value;
  },
  SET_SHOW_WHOLE_DAMS_TAB(state, showWholeDamsTab) {
    state.showWholeDamsTab = showWholeDamsTab;
  },
  // 获取浏览器定位
  SET_CURRENT_POSITION(state, data) {
    state.currentPosition = data;
  },
  SET_UPDATELIST(state, num) {
    state.updateList = num;
  }
};

const actions = {
  // 获取文件
  async getFile(context, fileToken) {
    return request({
      url: '/sys-storage/download',
      method: 'get',
      headers: {
        // "Content-Type": "application/x-www-form-urlencoded"
        'Content-Type': 'application/json;charset=UTF-8'
      },
      // responseType: "blob",
      params: {
        f8s: fileToken
      },
      responseType: 'blob'
    });
  },
  // 获取所有大坝信息
  async getAllDamInfos({ commit }, params) {
    return request({
      url: `${MICRO_DAM_URL}/allDams`,
      method: 'get',
      params
    });
  },
  // 根据电站获取大坝id
  async getCurrentDams({ commit }, {stationId}) {
    return new Promise((resolve, reject) => {
      request({
        url: `${WATCH_URL}/stationDams?stationId=${stationId}`,
        method: 'get'
      }).then((res) => {
        resolve(res);
      }).catch((err) => {
        reject(err);
      });
    });
  },
  // 根据门户获取电站,再根据电站获取大坝信息
  async getStationByPortal({ commit }) {
    // { portalId: store.state.portal.id, portalType: store.state.portal.type }
    return request({
      url: `${WATCH_URL}/damInfo`,
      params: { portalId: store.state.portal.id, type: store.state.portal.type }
    }).then(data => {
      return request({
        url: `${WATCH_URL}/stationDams`,
        params: { stationId: data.stationId }
      }).then(data => {
        commit('SET_CURRENT_DAM_INFO', data[0]);
        commit('SET_CURRENT_DAM_ID', data[0].damId);
        commit('SET_DAMS_INFO', data);
      });
    });
  },
  // 法定节假日-前端是否需要获取最新节假日,返回true（需要更新）
  async getHolidayNewest({ commit }) {
    return request({
      url: `${ATTENDANCE}/attendanceHoliday/holiday_newest`,
      method: 'get'
    });
  },
  // 公共接口获取节假日
  async getHolidaysFestivals({ commit }) {
    let dayjs = require('dayjs');
    return axios({
      url: `${process.env.VUE_APP_APIHUBS_URL}/holiday/get?year=${dayjs().format('YYYY')}&cn=1&size=366`,
      method: 'get'
    });
  },
  // 法定节假日-保存
  async getHolidaySave({ commit }, params) {
    return request({
      url: `${ATTENDANCE}/attendanceHoliday/holiday_save`,
      method: 'post',
      data: params
    });
  },
  // 根据项目标识获取当前关联大坝信息
  async getCurrentDamInfo({ commit }, { portalId, portalType }) {
    return new Promise((resolve, reject) => {
      request({
        url: `${MICRO_DAM_URL}/damInfo?portalId=${portalId}&type=${portalType}`,
        method: 'get'
      }).then((res) => {
        resolve(res);
      }).catch((err) => {
        reject(err);
      });
    });
  },
  // 获取根据ids获取用户信息
  async getRoleUser({ commit }, ids) {
    return request({
      url: `/sys-user/users?ids=${ids}`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  pushCancel({ commit }, cancel) {
    commit('PUSH_CANCEL', cancel);
  },
  clearCancel({ commit }) {
    commit('CLEAR_CANCEL');
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
