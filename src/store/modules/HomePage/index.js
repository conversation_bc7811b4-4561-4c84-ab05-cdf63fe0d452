import { ASSET_URL, SAFETY_URL, SEND_MESSAGE_URL, MICRO_DAM_URL, MAINTENANCE_URL, NEWSAFECHECK_URL, SYS_STORAGE_URL } from '@/plugins/constants/path';
import request from '@/utils/request';
import messageNotification from './messageNotification';

const state = () => ({});

const actions = {
  // 按章节ID查询章节内容
  async getContent({ commit }, id) {
    return request({
      url: `/anser-wiki/wiki/chapter`,
      method: 'get',
      params: {
        id
      },
      ctM: true
    });
  },
  async getFile({ commit }, fileToken) {
    return request({
      url: `${SYS_STORAGE_URL}/download`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      params: {
        f8s: fileToken
      },
      responseType: 'blob'
    });
  },
  // 设施设备统计
  async eequipFacilitieNum({ commit }) {
    return request({
      url: `${ASSET_URL}/statistic/eequip_facilities_num`,
      method: 'get'
    });
  },
  // 当前告警
  async getMessageAlert({ commit }, params) {
    return request({
      url: `${SEND_MESSAGE_URL}/message/alert`,
      method: 'get',
      params
    });
  },
  // 处理告警
  async processAlert({ commit }, data) {
    return request({
      url: `${SEND_MESSAGE_URL}/message/alert/process`,
      method: 'post',
      data: data
    });
  },
  async getList({ commit }, portalId) {
    return request({
      url: `${SAFETY_URL}/safety/hiddenDanger/statistics/project`,
      method: 'get',
      params: {
        portalId
      }
    });
  },
  async getListByPortal({ commit }, portalId) {
    return request({
      url: `${SAFETY_URL}/safety/hiddenDanger/statistics/rectifyState`,
      method: 'get',
      params: {
        portalId
      }
    });
  },
  // 安全监测
  async getMonitoringByPortal({ commit }, { portalId, type }) {
    return request({
      url: `${MICRO_DAM_URL}/codeInfo/codeStatus/count`,
      method: 'get',
      params: {
        portalId,
        type
      }
    });
  },
  // 易耗资产统计
  async consumableNum({ commit }) {
    return request({
      url: `${ASSET_URL}/statistic/consumable_num`,
      method: 'get'
    });
  },
  // 养护统计
  async statisticTask({ commit }, params) {
    return request({
      url: `${MAINTENANCE_URL}/maintenance/statistic_task`,
      method: 'get',
      params
    });
  },
  // 缺陷
  async bugDistribution({ commit }) {
    return request({
      url: `${NEWSAFECHECK_URL}/statistics/bug_distribution`,
      method: 'get'
    });
  },
  // 巡检状态统计
  async taskDistribution({ commit }, params) {
    return request({
      url: `${NEWSAFECHECK_URL}/statistics/task_distribution`,
      method: 'get',
      params
    });
  },
  // 巡检完成率统计
  async taskFinishRate({ commit }, params) {
    return request({
      url: `${NEWSAFECHECK_URL}/statistics/task_finish_rate`,
      method: 'get',
      params
    });
  }
};

export default {
  namespaced: true,
  state,
  actions,
  modules: {
    messageNotification
  }
};
