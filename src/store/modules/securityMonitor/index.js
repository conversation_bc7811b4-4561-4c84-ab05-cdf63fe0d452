import { WATCH_URL } from '@/plugins/constants/path';
import judge from './judge';
import detail from './detail';
import monitor from './monitor';
import task from './task';
import basic from './basic';
import abnormal from './abnormal';
import * as _ from 'lodash';
import request from '@/utils/request';

const state = () => ({
  badDamInfo: [],
  monitorCount: {},
  damSummary: {},
  ENUM_DATA: {
    CONDITION: [
      {
        label: '未监控',
        value: '0'
      },
      {
        label: '已监控',
        value: '1'
      }
    ],
    COLLECT_TYPE: [
      {
        autoTypeName: '全部',
        autoTypeId: 2
      },
      {
        autoTypeName: '人工',
        autoTypeId: 0
      },
      {
        autoTypeName: '自动化',
        autoTypeId: 1
      }
    ],
    TYPE: [
      {
        label: '请选择',
        value: ''
      },
      {
        label: '大坝',
        value: '0'
      },
      {
        label: '建筑物',
        value: '1'
      },
      {
        label: '部位',
        value: '2'
      },
      {
        label: '项目',
        value: '3'
      }
    ],
    IMPORTANT: [
      {
        label: '请选择',
        value: ''
      },
      {
        label: '普通',
        value: '0'
      },
      {
        label: '重要',
        value: '1'
      }
    ],
    IS_SIDE: [
      {
        label: '请选择',
        value: ''
      },
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  }
});
const getters = {
};
const mutations = {
  SET_MONITOR_PATTERN(state, monitorCount) {
    state.monitorCount = monitorCount;
  },
  SET_BAD_DAM_INFO(state, badDamInfo) {
    state.badDamInfo = badDamInfo;
  },
  SET_DAM_SUMMARY(state, damSummary) {
    state.damSummary = damSummary;
  }
};
const actions = {
  // 监控数量
  async getMonitorCount({commit}) {
    let url = `${WATCH_URL}/monitor/count`;
    await request.get(encodeURI(url)).then((res) => {
      commit('SET_MONITOR_PATTERN', res.data);
    });
  },
  // 监控情况修改
  async updateMonitorCondition({commit}, { damId, conditionType }) {
    return request.post(`${WATCH_URL}/monitor/updateMonitorCondition?damId=${damId}&conditionType=${conditionType}`);
  },
  // 查询大坝监控为异常的信息
  async getBadDamInfo({commit}) {
    let url = `${WATCH_URL}/monitor/badDamInfo`;
    await request.get(encodeURI(url)).then((res) => {
      commit('SET_BAD_DAM_INFO', res.data);
    });
  },
  // 单坝详情
  async getMonitorCodeInfo({commit}, params) {
    return request.get(`${WATCH_URL}/monitor/dam/codeInfo?params=${params}`);
  },
  // 大坝概况
  async getDamSurvey({commit}, params) {
    let url = `${WATCH_URL}/monitor/summaryInfo`;
    let query = _.cloneDeep(params.query);
    _.keys(query).forEach((key) => {
      if (!query[key] || query[key] === '' || query[key].length === 0) {
        delete query[key];
      }
    });
    url += `?params=${JSON.stringify(query)}`;
    if (params.size) {
      url += `&size=${params.size}`;
    }
    if (params.page) {
      url += `&page=${params.page}`;
    }
    if (params.sort) {
      url += `&sort=${params.sort}`;
    }
    await request.get((encodeURI(url))).then((res) => {
      commit('SET_DAM_SUMMARY', res.data);
    });
  },
  // 判断三级单位用户是否拥有多坝
  async isMoreDam({commit}) {
    return request.get(`${WATCH_URL}/monitor/isMoreDam`);
  },
  // 获取当前单坝
  async getCurrentSingleDam({commit}) {
    return request.get(`${WATCH_URL}/monitor/singleUserDamInfo`);
  },
  // 获取单坝监控点的测值信息
  async codeWatchValueInfo({commit}, {stationId, resultType}) {
    return request.get(encodeURI(`${WATCH_URL}/monitor/dam/codeWatchValueInfo?stationId=${stationId}&resultType=${resultType}`));
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
  modules: {
    judge,
    detail,
    monitor,
    task,
    basic,
    abnormal
  }
};
