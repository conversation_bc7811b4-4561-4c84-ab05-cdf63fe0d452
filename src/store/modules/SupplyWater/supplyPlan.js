import { DISPATCH_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = {
  rollOptions: [{
    value: '1',
    label: '是'
  }, {
    value: '0',
    label: '否'
  }],
  statusOptions: [{
    value: '0',
    label: '待审批'
  }, {
    value: '1',
    label: '已完成'
  }, {
    value: '2',
    label: '已废弃'
  }, {
    value: '4',
    label: '已退回'
  }],
  monthList: [{
    value: '1',
    monthCN: '1月',
    monthEN: 'Jan'
  }, {
    value: '2',
    monthCN: '2月',
    monthEN: 'Feb'
  }, {
    value: '3',
    monthCN: '3月',
    monthEN: 'Mar'
  }, {
    value: '4',
    monthCN: '4月',
    monthEN: 'Apr'
  }, {
    value: '5',
    monthCN: '5月',
    monthEN: 'May'
  }, {
    value: '6',
    monthCN: '6月',
    monthEN: 'Jun'
  }, {
    value: '7',
    monthCN: '7月',
    monthEN: 'Jul'
  }, {
    value: '8',
    monthCN: '8月',
    monthEN: 'Aug'
  }, {
    value: '9',
    monthCN: '9月',
    monthEN: 'Sep'
  }, {
    value: '10',
    monthCN: '10月',
    monthEN: 'Oct'
  }, {
    value: '11',
    monthCN: '11月',
    monthEN: 'Nov'
  }, {
    value: '12',
    monthCN: '12月',
    monthEN: 'Dec'
  }],
  // 滚动方式
  typeOptions: [{
    value: 'DEMAND',
    label: '原计划'
  }, {
    value: 'AVERAGE',
    label: '平均'
  }, {
    value: 'ANALOGY',
    label: '类比历史供水'
  }]
};

const getters = {};

const mutations = {};

const actions = {
  /** *********滚动计划接口 ***********/
  // 查看回滚列表
  async getWaterRollPlanList({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/page`,
      method: 'get',
      params: params
    });
  },
  // 查看滚动计划流程详情
  async getRollSupplyDetail({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/plan/detail`,
      method: 'get',
      params: params
    });
  },
  // 滚动计划生成
  async createRollInfo({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/save`,
      method: 'post',
      data: params
    });
  },
  // 删除滚动计划
  async deleteWaterRoll({ commit }, id) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/delete?planId=${id}`,
      method: 'delete'
    });
  },
  // 滚动计划下载
  async downloadRoll({ commit }, id) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/download?rollPlanId=` + id,
      method: 'get',
      responseType: 'blob'
    });
  },
  // 滚动计划表格内容获取
  async getRollTable({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/table`,
      method: 'get',
      params: params
    });
  },
  // 生成计划前校验是否有空数据
  async judgeTableIfEmpty({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/table/check`,
      method: 'post',
      data: params
    });
  },
  // 滚动计划 操作表格内容
  async editWaterRollValue({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/plan/detail`,
      method: 'post',
      data: params
    });
  },
  // 供水计划 手动提交processState状态
  async updateRollyPlan({ commit }, data) {
    return request({
      url: `${DISPATCH_URL}/waterRollPlan/commit`,
      method: 'post',
      data
    });
  },
  /** *********供水计划接口 ***********/
  // 查看供水计划流程详情
  async getWaterSupplyDetail({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/plan/detail`,
      method: 'get',
      params: params
    });
  },
  // 供水计划下载
  async downloadProvide({ commit }, id) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/download?supplyPlanId=` + id,
      method: 'get',
      responseType: 'blob'
    });
  },
  // 查看供水计划列表
  async getWaterSupplyList({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/page`,
      method: 'get',
      params: params
    });
  },
  // 删除供水计划
  async deleteWaterSupply({ commit }, id) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/delete?planId=${id}`,
      method: 'delete'
    });
  },
  // 下载供水计划
  async download({ commit }, data) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/download`,
      method: 'get',
      params: data
    });
  },
  // 供水计划 根据不同的导入方式 查询导入方案的详情
  async getImpDemandDetail({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/demand/import`,
      method: 'post',
      data: params
    });
  },

  // 供水计划 生成供水计划
  async saveWaterPlan({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/save`,
      method: 'post',
      data: params
    });
  },

  // 供水计划 操作表格内容
  async handlePlanTable({ commit }, data) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/table`,
      method: 'get',
      params: data
    });
  },
  // 供水计划 操作表格内容
  async editWaterSupplyValue({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/plan/detail`,
      method: 'post',
      data: params
    });
  },
  // 供水计划 获取表格内水厂月最大供水量
  async getMaxSupply({ commit }, data) {
    return request({
      url: `${DISPATCH_URL}/factoryWater/month/maxSupply`,
      method: 'get',
      params: data
    });
  },

  // 供水计划 获取表格内水厂月最大供水量
  async getFlowinfo({ commit }, data) {
    return request({
      url: `/sys-bpm/userTasks`,
      method: 'get',
      params: data
    });
  },
  // 供水计划 手动提交processState状态
  async updateSupplyPlan({ commit }, data) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/commit`,
      method: 'post',
      data
    });
  },

  // 自定义流程 清除表格缓存
  async clearCache({ commit }, params) {
    return request({
      url: `${DISPATCH_URL}/waterSupply/clear`,
      method: 'post',
      data: params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
