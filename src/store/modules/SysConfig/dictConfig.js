import { SYS_SYSTEM_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = {};

const getters = {};

const mutations = {};

const actions = {
  // 查询字典列表-分页
  async getDic({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/dictionary/page`,
      method: 'get',
      params: data
    });
  },
  // 查询字典详情列表
  async getDicDetail({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail/page`,
      method: 'get',
      params: data
    });
  },
  async getAllDicDetail({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail/code`,
      method: 'get',
      params: data
    });
  },
  async getDicStyle({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail/page`,
      method: 'get',
      params: data
    });
  },
  // 新增字典
  async addDic({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/dictionary`,
      method: 'post',
      data: data
    });
  },
  // 删除字典
  async deleteDic({ commit }, id) {
    return request({
      url: `${SYS_SYSTEM_URL}/dictionary/` + id,
      method: 'delete'
    });
  },
  // 保存、更新字典详情列表（全量跟新）
  async addDicDetail({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail`,
      method: 'post',
      data: data,
      transformRequest: [
        data => {
          return data;
        }
      ],
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  // 删除字典详情
  async deleteDicDetail({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail`,
      method: 'delete',
      params: data
    });
  },
  // 查询当前语言项
  async sort({ commit }, data) {
    return request({
      url: `${SYS_SYSTEM_URL}/lang/detail/sort`,
      method: 'post',
      data: data
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
