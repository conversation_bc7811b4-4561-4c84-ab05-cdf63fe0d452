import { SMARTWORK_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = {};

const getters = {};

const mutations = {};

const actions = {
  async saveEquipment({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/isc`,
      method: 'post',
      data: params
    });
  },
  async editEquipment({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/isc`,
      method: 'put',
      data: params
    });
  },
  async getEquipments({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/equipment/page`,
      method: 'get',
      params: params
    });
  },
  async deleteEquipment({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/equipment`,
      method: 'delete',
      params: params
    });
  },
  async getEquipmentsNew({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/equipment/newpage`,
      method: 'get',
      params: params
    });
  },
  // 更新设备状态
  async updateEquipmentState({ commit }) {
    return request({
      url: `${SMARTWORK_URL}/equipment/state`,
      method: 'get'
    });
  },
  // 获取最新设备台账分页
  async getEquipmentsPlusNew({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/equipment/plus/newpage`,
      method: 'get',
      params: params
    });
  },
  async queryIsMaster({ commit }, params) {
    return request({
      url: `${SMARTWORK_URL}/equipment/master`,
      method: 'get',
      params: params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
