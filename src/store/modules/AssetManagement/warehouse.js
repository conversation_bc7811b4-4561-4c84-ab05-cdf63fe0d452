import { ASSET_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = ({});

const getters = {};

const mutations = {};

const actions = {
  // 出入库--分页列表
  async list({ commit }, params) {
    return request({
      url: `${ASSET_URL}/inoutMain/page`,
      method: 'get',
      params
    });
  },
  // 出入库--详情
  async getInoutMainDetail({ commit }, params) {
    return request({
      url: `${ASSET_URL}/inoutMain/detail`,
      method: 'get',
      params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
