import { ASSET_URL } from '@/plugins/constants/path';
import request from '@/utils/request';
import axiosexport from '@/utils/export';

const state = ({});

const getters = {};

const mutations = {};

const actions = {
  // 新增
  async saveForm({ commit }, data) {
    return request({
      url: `${ASSET_URL}/capital/entry/insert`,
      method: 'post',
      data: data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  // 修改
  async editForm({ commit }, data) {
    return request({
      url: `${ASSET_URL}/capital/entry/update`,
      method: 'post',
      data: data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  // 删除
  async deleteForm({ commit }, ids) {
    return request({
      url: `${ASSET_URL}/capital/entry/delete`,
      method: 'get',
      params: { ids }
    });
  },
  // 批量导出
  async exportFile({ commit }, params) {
    return request({
      url: `${ASSET_URL}/capital/entry/export`,
      method: 'get',
      params,
      responseType: 'blob'
    });
  },
  // 下载模板 获取模板
  async downloadTemplate({ commit }, params) {
    return request({
      url: `${ASSET_URL}/capital/entry/downTemplate`,
      method: 'get',
      params,
      responseType: 'blob'
    });
  },
  async uploadTemplate({ commit }, data) {
    return request({
      url: `${ASSET_URL}/capital/entry/import`,
      method: 'post',
      data,
      responseType: 'blob'
    });
  },
  // 列表
  async getPageList({ commit }, params) {
    return request({
      url: `${ASSET_URL}/capital/entry/selectPage`,
      method: 'get',
      params: params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
