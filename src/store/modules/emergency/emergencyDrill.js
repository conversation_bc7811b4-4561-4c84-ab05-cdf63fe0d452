import { FLOOD_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const actions = {
  // 查询应急演练
  getEmergencyDrill({commit}, params) {
    return request({
      url: `${FLOOD_URL}/emergency-drills`,
      mehtods: 'get',
      params
    });
  },
  // 根据ID查询一笔应急演练数据
  getEmergencyDrillById({commit}, id) {
    return request({
      url: `${FLOOD_URL}/emergency-drills/${id}`,
      mehtods: 'get'
    });
  },
  // 保存应急演练
  saveEmergencyDrill({commit}, params) {
    return request({
      url: `${FLOOD_URL}/emergency-drills`,
      mehtods: 'post',
      params
    });
  },
  // 根据文档id删除一笔数据
  delEmergencyDrillFiles({commit}, id) {
    return request({
      url: `${FLOOD_URL}/emergency-drill-files/${id}`,
      mehtods: 'delete'
    });
  },
  // 更新应急演练
  updatEemergencyDrill({commit}, params) {
    return request({
      url: `${FLOOD_URL}/emergency-drills/${params.emergencyDrillId}`,
      mehtods: 'patch',
      params
    });
  }
};

export default {
  actions
};
