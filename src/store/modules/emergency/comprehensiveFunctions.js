import request from '@/utils/request';
const state = () => ({
});

const getters = {
};

const mutations = {};
const actions = {
  // 历史搜索--新增搜索记录
  addHistory({commit, state}, data) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/search/addHistory`,
      method: 'POST',
      data
    });
  },
  // 知识搜索--精确/模糊 sType 1精确/2模糊 sKey 搜索内容
  findKnowledge({commit}, params) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/search/findKnowledge`,
      params
    });
  },
  // 历史搜索--搜索记录
  listHistory({commit, state}, username) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/search/listHistory`,
      params: { username }
    });
  },
  // 历史搜索--搜索记录(主键id，不为空--删除单条；为空则清空)
  delHistory({commit, state}, params) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/search/delHistory`,
      params
    });
  },
  // 知识利用--应用--新增or修改--传List
  addOrUpdApplyKnowledge({commit, state}, data) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/application/addOrUpdApplyKnowledge`,
      method: 'POST',
      data
    });
  },
  // 知识利用--删除
  delApplyKnowledge({commit, state}, id) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/application/delApplyKnowledge`,
      params: { id }
    });
  },
  // 知识利用--导出
  exportKnowledge({commit, state}, params) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/application/exportKnowledge`,
      params,
      responseType: 'blob'
    });
  },
  // 知识更新--分页查询
  listKnowledgeByPage({commit, state}, params) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/update/listKnowledgeByPage`,
      params
    });
  },
  // 知识更新--删除
  delKnowledge({commit, state}, id) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/update/delKnowledge`,
      params: { id }
    });
  },
  // 知识更新--新增or修改
  addOrUpdKnowledge({commit, state}, data) {
    return request({
      url: `/yxyw-emergency/comprehensive/knowledge/udpate/addOrUpdKnowledge`,
      method: 'POST',
      data
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
