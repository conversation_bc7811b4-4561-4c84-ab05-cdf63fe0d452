import { MICRO_DAM_URL } from '@/plugins/constants/path';
import _ from 'lodash';
import request from '@/utils/request';

const state = () => ({
  distributionList: [],
  allCode: [],
  codeGroup: [],
  groupCodes: []
});

const getters = {
};

const mutations = {
  // 分布图导航列表
  SET_DISTRIBUTION_ITEM_LIST(state, distributionList) {
    state.distributionList = distributionList;
  },
  SET_ALL_CODE(state, allCode) {
    state.allCode = allCode;
  },
  SET_CODE_GROUP(state, codeGroup) {
    state.codeGroup = codeGroup;
  },
  SET_GROUP_CODES(state, groupCodes) {
    state.groupCodes = groupCodes;
  }
};

const actions = {
  // 根据导航查询未分组的测点
  async getAllCode({commit}, {projectId, instrId, damId}) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/findCodes?projectId=${projectId}&instrId=${instrId}`,
      method: 'get'
    });
  },
  // 测点组列表
  async getCodeGroup({commit}, itemId) {
    return request({
      url: `${MICRO_DAM_URL}/dam/findCodeGroup/${itemId}`,
      method: 'get'
    });
  },
  // 点击分组查询测点
  async getGroupCodes({commit}, groupId) {
    return request({
      url: `${MICRO_DAM_URL}/dam/findGroupCodes/${groupId}`,
      method: 'get'
    });
  },
  // 将已选测点分组
  async updateCodeGrouping({commit}, {groupId, codeIds}) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/codeGrouping/${groupId}?codeIds=${codeIds}`,
      method: 'post'
    });
  },
  // 修改测点组
  async updateGroupName({commit}, {groupId, groupName}) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/updateCodeGroupName/${groupId}?groupName=${encodeURIComponent(groupName)}`,
      method: 'patch'
    });
  },
  // 取消分组
  async cancelGroupCode({commit}, {groupId, codeIds}) {
    return request({
      url: `${MICRO_DAM_URL}/dam/cancelGroup/${groupId}?codeIds=${codeIds}`,
      method: 'patch'
    });
  },
  // 新建测点组
  async saveCodeGroup({commit}, {itemId, groupName}) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/saveCodeGroup/${itemId}`,
      data: groupName,
      method: 'post'
    });
  },
  // 删除测点组
  async deleteCodeGroup({commit}, groupIds) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/deleteCodeGroup?groupIds=${groupIds}`,
      method: 'delete'
    });
  },
  // 自动分组
  async autoGroup({commit}, {projectId, params}) {
    let url = `${MICRO_DAM_URL}/dam/${this.state.monitor.sys.code.selectTreeParams.damId}/autoCodeGroup/${projectId}`;
    let param = _.cloneDeep(params);
    let name = '';
    param.forEach(group => {
      group.codeIds = group.codeIds.join(',');
      name += JSON.stringify(group) + ';';
    });
    url += `?params=${encodeURIComponent(name)}`;
    return request({
      url,
      method: 'post'
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
