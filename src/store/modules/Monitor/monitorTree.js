import * as _ from 'lodash';

import { MICRO_DAM_URL } from '@/plugins/constants/path';

const state = ({
  treeData: null,
  treeLoading: false,
  currentTreeDamId: null, // 当前整改大坝copyId, 跟新的大坝id做对比 判断是否变化 如变化需要重新刷新树 否则不需要
  listTreeData: {},
  listTreeDataLoading: false,
  exportStatusParam: '',
  treeIds: {}
});

const getters = {
};

const mutations = {
  SET_MONITOR_TREE(state, val) {
    state.treeData = val;
  },
  SET_TREE_DAM_ID(state, val) {
    state.currentTreeDamId = val;
  },
  SET_TREE_LOADING(state, val) {
    state.treeLoading = val;
  },
  SET_LIST_TREE_DATA(state, listTreeData) {
    state.listTreeData = listTreeData;
  },
  SET_LIST_TREE_DATA_LOADING(state, listTreeDataLoading) {
    state.listTreeDataLoading = listTreeDataLoading;
  },
  SET_CURRENT_MONITOR_TREE_IDS(state, treeIds) {
    state.treeIds = treeIds;
  }
};

const actions = {
  // 测点树列表
  async getMonitorTree({commit, state}, params) {
    state.exportStatusParam = '';
    let url = `${MICRO_DAM_URL}/monitorData/dam/${params.damId}/codeStatusTree`;
    let query = _.cloneDeep(params.query);
    if (!_.isEmpty(params.query)) {
      _.keys(query).forEach(key => {
        if (!_.isNumber(query[key])) {
          if (!query[key] || query[key] === '' || query[key].length === 0) {
            delete query[key];
          }
        }
      });
      url += `?&params=${JSON.stringify(query)}`;
      state.exportStatusParam = `&params=${JSON.stringify(query)}`;
    }
    if (params.isDialog) {
      state.treeLoading = true;
    } else {
      state.listTreeDataLoading = true;
    }
    await this.$axios.get(encodeURI(url)).then((res) => {
      if (params.isDialog) {
        commit('SET_MONITOR_TREE', res.data);
        state.treeLoading = false;
      } else {
        commit('SET_LIST_TREE_DATA', res.data);
        state.listTreeDataLoading = false;
      }
    }).catch(() => {
      if (params.isDialog) {
        state.treeLoading = false;
      } else {
        state.listTreeDataLoading = false;
      }
    });
  },
  async getLazeCodeList({ state }, params) {
    let url = `${MICRO_DAM_URL}/monitorData/dam/${params.damId}/codeStatusTreeCode?params=${JSON.stringify(params.query)}`;
    return this.$axios.get(encodeURI(url));
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
