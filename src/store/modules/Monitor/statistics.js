import _ from 'lodash';

import { MICRO_DAM_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = ({
  statisticsCode: [],
  statisticsDefaultCode: [],
  statisticsVector: [],
  instrId: null,
  tableData: [],
  tableLoading: false,
  maxNum: 200
});
const mutations = {
  // 选择统计测点
  SET_STATISTICS_CODE(state, statisticsCode) {
    state.statisticsCode = statisticsCode;
  },
  // 选择默认统计测点
  SET_STATISTICS_DEFAULT_CODE(state, statisticsDefaultCode) {
    state.statisticsDefaultCode = statisticsDefaultCode;
  },
  // 选择统计分量
  SET_STATISTICS_VECTOR(state, statisticsVector) {
    state.statisticsVector = statisticsVector;
  },
  // 选择仪器id
  SET_INSTR_ID(state, instrId) {
    state.instrId = instrId;
  },
  // 统计数据
  SET_STATISTICS_DATA(state, tableData) {
    state.tableData = tableData;
  },
  // 统计数据Loading
  SET_DATA_LOADING(state, tableLoading) {
    state.tableLoading = tableLoading;
  },
  // 显示最多测点个数
  SET_MAX_NUM(state, maxNum) {
    state.maxNum = maxNum;
  },
  // 清空数据
  RESET_STORE_DATA(state) {
    Object.keys(state).forEach(key => {
      state[key] = _.isArray(state[key]) ? [] : null;
    });
  }
};
const actions = {
  // 监测数据导航树
  async getCodeVector({ commit }, params) {
    return request({
      url: `${MICRO_DAM_URL}/dam/${params.damId}/findCodeInfoList?params=${encodeURI(JSON.stringify(params.query))}&projectId=${params.projectId}&instrId=${params.instrId}`,
      methos: 'get'
    });
  },
  // 时间段特征值、月份特征值、年度特征值、极值、首尾差值
  async getStaticBy({ commit }, {type, params}) {
    let url = `${MICRO_DAM_URL}/code/static/${type}?`;
    _.keys(params).forEach(key => {
      url += `&${key}=${params[key]}`;
    });
    return request({
      url,
      method: 'post'
    });
  }
};
export default {
  namespaced: true,
  state,
  actions,
  mutations
};
