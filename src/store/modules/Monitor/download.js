/*eslint-disable*/
import { MICRO_DAM_URL } from '@/plugins/constants/path';
import request from '@/utils/request';
const state = ({
});
const mutations = {
};
const actions = {
  getDownData({commit}, param) {
    return request({
      url: `${MICRO_DAM_URL}/img/getImg`,
      params: param,
      method: 'get'
    });
  },
  getDownFile({commit}, param) {
    let GET_FILE = `${MICRO_DAM_URL}/file/download?mongoId=${param}`;
    return request({
      url: GET_FILE,
      params: param,
      method: 'get'
    });
  },
  getDownloadExcel({commit}, obj) {
    let url = `${MICRO_DAM_URL}/excel/${obj.type}/create`;
    return request({
      url,
      params: obj.param,
      responseType: 'blob',
      headers: {'Fawkes-Auth': localStorage.getItem('cs_access_token')},
      method: 'get',
    });
  },
  getDownloadStatisticsExcel({commit}, obj) {
    let url = `${MICRO_DAM_URL}/codeValue/static/${obj.type}/excel`;
    return request({
      url,
      params: obj.param,
      responseType: 'blob',
      headers: {'Fawkes-Auth': localStorage.getItem('cs_access_token')},
      method: 'get',
    });
  }
};
export default {
  namespaced: true,
  state,
  actions,
  mutations
};
