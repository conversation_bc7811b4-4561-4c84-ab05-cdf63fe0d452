import { MICRO_DAM_URL } from '@/plugins/constants/path';
const state = ({
});

const getters = {
};

const mutations = {
};

const actions = {
  async findBreakdown({state}, params) {
    let url = `${MICRO_DAM_URL}/monitorData/code/findBreakdown?size=${params.size}&damId=${params.damId}&stationId=${params.stationId}&page=${params.page}&params=${JSON.stringify(params.query)}`;
    if (params.codeId) {
      url += `&codeId=${params.codeId}`;
    }
    if (params.projectId) {
      url += `&projectId=${params.projectId}`;
    }
    if (params.groupId) {
      url += `&groupId=${params.groupId}`;
    }
    if (params.instrId) {
      url += `&instrId=${params.instrId}`;
    }
    if (params.startTime) {
      url += `&startTime=${params.startTime} 00:00:00`;
    }
    if (params.endTime) {
      url += `&endTime=${params.endTime} 23:59:59`;
    }
    return this.$axios.get(encodeURI(url));
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
