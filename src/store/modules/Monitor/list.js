import * as _ from 'lodash';
import { MICRO_DAM_URL } from '@/plugins/constants/path';
const DAM_URL = '/basedam';

const state = ({
  exportBaseParam: '',
  tableLoading: true,
  auditTableData: {
    content: []
  }
});

const getters = {
};

const mutations = {
  SET_TABLE_LOADING(state, tableLoading) {
    state.tableLoading = tableLoading;
  },
  SET_TABLE_DATA(state, auditTableData) {
    state.auditTableData = auditTableData;
  }
};

const actions = {
  async getMonitorData({commit}, params) {
    state.tableLoading = true;
    state.exportBaseParam = '';
    let url = `${MICRO_DAM_URL}/dams/monitor/data?size=${params.size}&page=${params.page}`;
    if (!_.isEmpty(params.query)) {
      let query = _.cloneDeep(params.query);
      _.keys(query).forEach((key) => {
        if ((!query[key] || query[key] === '') && !_.isNumber(query[key])) {
          delete query[key];
        }
      });
      url += `&params=${JSON.stringify(query)}`;
      state.exportBaseParam += `&params=${JSON.stringify(query)}`;
    }
    if (params.sort) {
      url += `&sort=${params.sort}`;
      state.exportBaseParam += `&sort=${params.sort}`;
    }
    await request({
      url: encodeURI(url),
      method: 'get'
    }).then(res => {
      state.auditTableData = res;
      state.tableLoading = false;
    }).catch(() => {
      state.tableLoading = false;
    });
  },
  // 获取仪器测点环形图数据
  async getInstrPointData({commit}, damId) {
    let url = `${MICRO_DAM_URL}/codeInfo/codeStatus/count`;
    if (damId) {
      url += `?damIds=${_.isArray(damId) ? damId.join(',') : damId}`;
    }
    return request({
      url,
      method: 'get'
    });
  },
  // 获取监测系统值
  async getDamMonitorSysStatistics({commit}, params) {
    let url = `${DAM_URL}/damMonitorSysStatistics`;
    if (params && params.status) {
      url += `?status=${params.status}`;
    }
    return request({
      url,
      method: 'get'
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
