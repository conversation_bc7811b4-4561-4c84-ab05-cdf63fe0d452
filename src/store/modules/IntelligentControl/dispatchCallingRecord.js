import request from '@/utils/request';

const state = {};
const getters = {};
const mutations = {};
const actions = {
  // 获取调度通话记录
  async getCallingRecords({commit}, params) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/page',
      method: 'get',
      params
    });
  },
  // 获取组织机构
  async getOrgs({commit}) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/home/<USER>'
    });
  },
  // 根据组织机构id获取工程
  async getProjectsByOrgId({commit}, orgNo) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/home/<USER>',
      params: {orgNo}
    });
  },
  // 导出调度通话记录
  async exportCallingRecord({commit}, params) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/export',
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      },
      params
    });
  },
  // 根据id获取详细信息
  async getDetailById({commit}, id) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/getDetailById',
      method: 'get',
      params: { id }
    });
  },
  // 编辑通话记录
  async updateCallRecord({commit}, data) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/updateCallRecord',
      method: 'post',
      data
    });
  },
  // 新增通话记录
  async addCallRecord({commit}, data) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/save',
      method: 'post',
      data
    });
  },
  // 获取 副标题
  async getDetailTitle({commit}, projectId) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/detail/otherInfo',
      method: 'get',
      params: { projectId }
    });
  },
  // 获取 历史通话人
  async getHistoryCallers({commit}, projectId) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/person/record',
      method: 'get',
      params: { projectId }
    });
  },
  // 删除通话记录
  async deleteCallRecord({commit}, id) {
    return request({
      url: '/yx-dispatch/app/dispatch/call/record/deleteCallRecord',
      method: 'get',
      params: { id }
    });
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
