import request from '@/utils/request';

const state = {
  dicts: null,
  currentArea: ''
};
const getters = {};
const mutations = {
  SET_DICT(state, data) {
    state.dicts = data;
  },
  SET_CURRENT_AREA(state, data) {
    state.currentArea = data;
  }
};
const actions = {
  // 拓扑图-各工程当前数据
  getTopProjectData({commit}) {
    return request({
      url: '/yx-dispatch/topologyMap/project/data'
    });
  },
  // 实时工况-片区工程统计
  getAreaProjectList({commit}, params) {
    return request({
      url: '/yx-dispatch/workingConditions/area/stats',
      params
    });
  },
  // 实时工况-片区下各工程信息
  getProjectInfoByArea({commit}, params) {
    return request({
      url: '/yx-dispatch/workingConditions/area/project/info',
      params
    });
  },
  // 实时工况-片区下各工程数量
  getProjectNumersByArea({commit}, params) {
    return request({
      url: '/yx-dispatch/workingConditions/area/project/num',
      params
    });
  },
  async getDict({commit}) {
    const {status, data} = await request({
      url: '/yx-dispatch/dict/all'
    });
    if (status) {
      commit('SET_DICT', data);
      commit('SET_CURRENT_AREA', data.SituationAreaEnum[0].key);
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
