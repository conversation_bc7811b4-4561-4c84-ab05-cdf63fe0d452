import request from '../../../utils/request';

const states = {
  dicts: null
};
const mutations = {
  SET_DICT(state, data) {
    state.dicts = data;
  }
};
const actions = {
  // 获取交接班记录
  async getHandoverRecords({commit}, params) {
    return request({
      url: '/yx-dispatch/handover/flow/page',
      params
    });
  },
  // 获取字典
  async getDict({commit}) {
    const {status, data} = await request({
      url: '/yx-dispatch/dict/all'
    });
    if (status) {
      commit('SET_DICT', data);
    }
  },
  // 查询所有班组人员
  async getAllGroupMembers() {
    return request({
      url: '/yx-dispatch/handover/group/person/all'
    });
  },
  // 设置班组人员
  async setGroupMember({commit}, data) {
    return request({
      url: '/yx-dispatch/handover/group/person/config',
      method: 'post',
      data
    });
  },
  // 导出交接班记录
  async exportHandoverRecord({commit}, id) {
    return request({
      url: '/yx-dispatch/export/shiftLog',
      params: {
        id
      },
      responseType: 'blob'
    });
  },
  // 批量导出交接班记录
  async exportMultiHandoverRecord({commit}, params) {
    return request({
      url: '/yx-dispatch/export/shiftLogMulti',
      params,
      responseType: 'blob'
    });
  }
};

export default {
  namespaced: true,
  states,
  mutations,
  actions
};
