import { SYS_MONITOR_URL, SYS_STORAGE_URL } from '@/plugins/constants/path';
import request from '@/utils/request';
import axiosexport from '@/utils/export';

const state = {};

const getters = {};

const mutations = {};

const actions = {
  // 保存设置
  async configLog({ commit }, data) {
    return request({
      url: `${SYS_MONITOR_URL}/log/config`,
      method: 'post',
      data: data
    });
  },
  // 获取当前正在使用的配置信息
  async configUsing({ commit }) {
    return request({
      url: `${SYS_MONITOR_URL}/log/config/using`,
      method: 'get'
    });
  },
  // 获取折线图的数据
  async getLine({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/log/stat`,
      method: 'get',
      params: {
        param: params
      }
    });
  },
  // 获取接口访问量排行
  async getRank({ commit }) {
    return request({
      url: `${SYS_MONITOR_URL}/log/stat/rank`,
      method: 'get'
    });
  },
  // 获取访问一个接口的平均耗时
  async getAvgTime({ commit }) {
    return request({
      url: `${SYS_MONITOR_URL}/log/stat/avg`,
      method: 'get'
    });
  },
  // 获取统计维度枚举
  async getEnum({ commit }) {
    return request({
      url: `${SYS_MONITOR_URL}/log/stat/enum`,
      method: 'get'
    });
  },
  // 备份分页查询
  async getLogData({ commit }, data) {
    return request({
      url: `${SYS_MONITOR_URL}/log/backup/page`,
      method: 'get',
      params: data
    });
  },
  // 备份指定日期日志
  async backupLog({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/log/backup`,
      method: 'get',
      params: params
    });
  },
  // 下载备份日志文件
  async download({ commit }, params) {
    return axiosexport({
      url: `${SYS_STORAGE_URL}/zip`,
      method: 'get',
      params: params,
      responseType: 'blob',
      transformRequest: function (data) {
      // 对 data 进行任意转换处理
        return JSON.stringify(data);
      },
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  // 删除备份日志文件
  async deleteLog({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/log/backup`,
      method: 'delete',
      params: params
    });
  },
  // 接口日志列表
  async getAllInterfaceLog({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/apiLog/page`,
      method: 'get',
      params: params
    });
  },
  // 接口日志详情
  async getInterfaceLog({ commit }, id) {
    return request({
      url: `${SYS_MONITOR_URL}/log/api`,
      method: 'get',
      params: {
        id: id
      }
    });
  },
  // 错误日志列表
  async getAllErrorLog({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/exceptionLog/page`,
      method: 'get',
      params: params
    });
  },
  // 错误日志详情
  async getErrorLog({ commit }, id) {
    return request({
      url: `${SYS_MONITOR_URL}/log/exception`,
      method: 'get',
      params: {
        id: id
      }
    });
  },
  // 安全事件列表
  async getAllSecurityLog({ commit }, params) {
    return request({
      url: `${SYS_MONITOR_URL}/securityLog/page`,
      method: 'get',
      params: params
    });
  },
  // 安全事件详情
  async getSecurityLog({ commit }, id) {
    return request({
      url: `${SYS_MONITOR_URL}/log/security`,
      method: 'get',
      params: {
        id: id
      }
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
