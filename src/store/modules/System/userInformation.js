import { SYS_USER_URL, SYS_AUTH_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = {
  imageUrl: ''
};

const getters = {};

const mutations = {
  AVATAR_IMG(state, imageUrl) {
    state.imageUrl = imageUrl;
  }
};

const actions = {
  // 修改密码
  async editPassword({ commit }, data) {
    return request({
      url: `${SYS_USER_URL}/user/pwd`,
      method: 'put',
      data: data
    });
  },
  // 退出登录
  async exit({ commit }) {
    return request({
      url: `${SYS_AUTH_URL}/oauth/exit`,
      method: 'DELETE'
    });
  }
};
export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
