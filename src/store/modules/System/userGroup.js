import { SYS_USER_URL } from '@/plugins/constants/path';
import request from '@/utils/request';

const state = {};

const getters = {};

const mutations = {};

const actions = {
  // 用户组树
  async getUserOrgTree({ commit }) {
    return request({
      url: `${SYS_USER_URL}/user_group/tree`,
      method: 'get'
    });
  },
  // 新增用户组
  async addUserGroup({ commit }, data) {
    return request({
      url: `${SYS_USER_URL}/user_group`,
      method: 'POST',
      data: data
    });
  },
  // 修改用户组
  async updataUserGroup({ commit }, data) {
    return request({
      url: `${SYS_USER_URL}/user_group`,
      method: 'put',
      data: data
    });
  },
  // 删除用户组
  async deleteUserGroup({ commit }, id) {
    return request({
      url: `${SYS_USER_URL}/user_group`,
      method: 'DELETE',
      params: {
        userGroupId: id
      }
    });
  },
  // 用户组下用户列表分页查询
  async getUserTreePage({ commit }, params) {
    return request({
      url: `${SYS_USER_URL}/user_group/users/page`,
      method: 'get',
      params: params
    });
  },
  // 更新用户组下用户
  async updataCheckOrgUser({ commit }, data) {
    return request({
      url: `${SYS_USER_URL}/user_group/user`,
      method: 'post',
      data: data
    });
  },
  // 删除用户组下用户
  async deleteCheckOrgUser({ commit }, data) {
    return request({
      url: `${SYS_USER_URL}/user_group/user`,
      method: 'DELETE',
      params: data
    });
  },
  // 用户组中，组织机构已选择的人员回显树
  async getCheckOrgUserTree({ commit }, id) {
    return request({
      url: `${SYS_USER_URL}/user_group/org_tree/user_check`,
      method: 'get',
      params: {
        userGroupId: id
      }
    });
  },
  // 查询当前人员组信息是否属于用户分组
  async getUserGroup(params) {
    return request({
      url: '/sys-bpm/process/userGroup',
      method: 'get',
      params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
