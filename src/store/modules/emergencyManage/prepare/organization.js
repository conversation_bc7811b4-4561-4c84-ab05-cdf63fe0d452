import { EMERGENCY_URL } from '@/plugins/constants/path';
import request from '@/utils/request';
const state = ({
});
const mutations = {
};
const actions = {
  addOrganization({commit}, data) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/add`,
      method: 'post',
      data
    })        
  },
  delOrganization({commit}, id) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/delete/${id}`,
      method: 'delete',
    })          
  },
  exportExcelOrganization({commit}, ids) {
    let url = `${EMERGENCY_URL}/organizationalStructure/exportExcel/${ids}`;
    return this.$axios.get(url, {
      responseType: 'blob'
    });
  },
  getOrganizeTableData({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/list?pageNo=${obj.pageNo - 1}&pageSize=${obj.pageSize}&name=${obj.name}&startTime=${obj.startTime}&endTime=${obj.endTime}&eventTypeId=${obj.eventTypeId}&projectId=${obj.projectId}`,
      method: 'get',
    })         
  },
  getOrganizeTableDataTree({commit}, id) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/list/org/tree/${id}`,
      method: 'get',
    })  
  },
  getOrganizationList({commit}, params) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/list`,
      method: 'get',
      params
    })      
  },
  updateOrganizationStructure({commit}, data) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/update`,
      method: 'put',
      data
    })      
  },
  getOneOrganizationStructure({commit}, id) {
    return request({
      url:`${EMERGENCY_URL}/organizationalStructure/${id}`,
      method: 'get',
    })        
  },
  // 应急组织
  organizeDataAdd({ commit }, data) {
    return request({
      url:`${EMERGENCY_URL}/organization/add`,
      method: 'post',
      data
    })     
  },
  organizeDataAddReady({ commit }, data) {
    return request({
      url:`${EMERGENCY_URL}/organization/add/already`,
      method: 'post',
      data
    })      
  },
  organizeDataPutRename({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organization/update/rename?id=${obj.id}&orgName=${obj.orgName}`,
      method: 'put',
    })   
  },
  organizeDataDelete({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organization/remove?orgId=${obj.orgId}&structureId=${obj.structureId}`,
      method: 'delete',
    })   
  },
  getOrganizeData({ commit }, id) {
    return request({
      url:`${EMERGENCY_URL}/organization/${id}`,
      method: 'get',
    })     
  },
  getOrgFlagList({ commit }, data) {
    // return this.$axios.post(`${EMERGENCY_URL}/organization/recur/org/flat`, param);
    return request({
      url:`${EMERGENCY_URL}/organization/recur/org/flat`,
      method: 'post',
      data
    })         
  },
  getOrgList({ commit }, param) {
    return request({
      url:`${EMERGENCY_URL}/organization/recur/org?orgId=${param.orgId}&structureId=${param.structureId}`,
      method: 'get',
    })        
  },
  // 应急组织人员
  getOrganizePersonTableDataById({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organizationMember/get/member?memberId=${obj.memberId}&orgId=${obj.orgId}`,
      method: 'get',
    })     
  },
  getOrganizePersonTableData({ commit }, id) {
    return request({
      url:`${EMERGENCY_URL}/organizationMember/list/${id}`,
      method: 'get',
    })         
  },
  OrganizePersonTableDataAdd({ commit }, data) {
    return request({
      url:`${EMERGENCY_URL}/organizationMember/add`,
      method: 'post',
      data
    })  
  },
  OrganizePersonTableDataDelete({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organizationMember/delete?memberId=${obj.memberId}&orgId=${obj.orgId}`,
      method: 'delete',
    })  
  },
  OrganizePersonTableDataPut({ commit }, data) {
    return request({
      url:`${EMERGENCY_URL}/organizationMember/update`,
      method: 'put',
      data
    })  
  },
  OrganizationDutyPut({ commit }, obj) {
    return request({
      url:`${EMERGENCY_URL}/organization/update/orgduty?id=${obj.id}&orgDuty=${obj.orgDuty}`,
      method: 'put',
    })      
  },
  // 推送人员树状结构
  getAllOrg({ commit }) {
    return request({
      url:`${EMERGENCY_URL}/organization/all/org/flat/${this.state.shared.stationId}`,
      method: 'get',
    })      
  }
};
export default {
  namespaced: true,
  state,
  actions,
  mutations
};
