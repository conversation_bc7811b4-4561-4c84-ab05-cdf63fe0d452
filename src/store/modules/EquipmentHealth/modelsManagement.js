import request from '@/utils/request';
import {YXSZY_HEALTH} from '@/plugins/constants/path';

export default {
  namespaced: true,
  state: {
    items: null
  },
  mutations: {
    SET_ITEMS(state, payload) {
      state.items = payload;
    }
  },
  actions: {
    // 获取所有综合评价模型列表
    async getAllModels() {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/GetAll@V1.0`,
        method: 'get'
      });
    },
    // 新增一条模型数据
    async addModel(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Insert@V1.0`,
        method: 'post',
        data
      });
    },
    // 编辑模型
    async editModel(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Update@V1.0`,
        method: 'put',
        data
      });
    },
    // 删除模型
    async deleteModel(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/DeleteByID@V1.0`,
        method: 'delete',
        params: {
          ID: data
        }
      });
    },
    // 通过选择的模型加载所有模型评价项
    async getModelItemsById(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Item/GetByModelID@V1.0`,
        params: {
          ModelID: data
        }
      });
    },
    // 获取设备标签列表
    getDeviceLabels() {
      return request({
        url: `/YXSZY/Assets/Equipment/Std/GetFlagHaveList@V1.0`
      });
    },
    // 获取重要程度选择列表
    getImportanceList() {
      return request({
        url: `${YXSZY_HEALTH}/Importance/Std/GetAll@V1.0`
      });
    },
    // 获取劣化程度选择列表
    getDeteriorationList() {
      return request({
        url: `${YXSZY_HEALTH}/Deterioration/Std/GetAll@V1.0`
      });
    },
    async getItems({commit, state, dispatch}) {
      if (!state.items) {
        const data = await Promise.all([
          dispatch('getDeviceLabels'),
          dispatch('getImportanceList'),
          dispatch('getDeteriorationList')
        ]);
        const properties = ['deviceLabels', 'importanceList', 'deteriorationList'];
        commit('SET_ITEMS', data.map(item => item.data).reduce((acc, cur, index) => {
          return Object.assign(acc, {[properties[index]]: cur});
        }, {}));
      }
    },
    // 新增一条模型评价项
    addModelItem(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Item/Insert@V1.0`,
        method: 'post',
        data
      });
    },
    // 编辑选择的模型评价项
    editModelItem(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Item/Update@V1.0`,
        method: 'put',
        data
      });
    },
    // 删除当前选择的模型评价项
    deleteModelItem(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Item/DeleteByID@V1.0`,
        method: 'delete',
        params: {ID: data}
      });
    },
    // 通过选择的模型加载所有模型评价条件
    async getModelCondition(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Condition/GetByModelID@V1.0`,
        params: {
          ModelID: data
        }
      });
    },
    // 新增一条模型评价条件
    addModelCondition(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Condition/Insert@V1.0`,
        method: 'post',
        data
      });
    },
    // 编辑选择的模型评价条件
    editModelCondition(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Condition/Update@V1.0`,
        method: 'put',
        data
      });
    },
    // 删除当前选择的模型评价条件
    deleteModelCondition(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Condition/DeleteByID@V1.0`,
        method: 'delete',
        params: {ID: data}
      });
    }
  }
};
