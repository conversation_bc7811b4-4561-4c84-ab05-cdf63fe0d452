import request from '@/utils/request';
import {YXSZY_HEALTH} from '@/plugins/constants/path';

export default {
  namespaced: true,
  state: {
    models: []
  },
  mutations: {
    SET_MODELS(state, payload) {
      state.models = payload;
    }
  },
  actions: {
    getDeviceTree(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Binding/GetByStationID@V1.0`,
        params: {
          StationID: data
        }
      });
    },
    getAllModels({commit}) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Std/GetSelectList@V1.0`
      }).then(res => {
        if (res.status) {
          commit('SET_MODELS', res.data);
        }
      });
    },
    bindModel(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Binding/Binding@V1.0`,
        method: 'post',
        data
      });
    },
    unbindModel(props, data) {
      return request({
        url: `${YXSZY_HEALTH}/Multi/Evaluation/Model/Binding/UnBinding@V1.0`,
        method: 'post',
        data: {
          EquipmentID: data
        }
      });
    },
    getBindingModels(props, data) {
      return request({
        url: '/YXSZY/Health/Multiple/Evaluation/Product/GetBindingModelByProductID@V1.0',
        params: {
          ProductID: data
        }
      });
    }
  }
};
