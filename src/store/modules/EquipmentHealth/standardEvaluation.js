import request from '@/utils/request';
import { YXSZY_HEALTH, YXSZY_BASIC, YXSZY_MONITOR_URL } from '@/plugins/constants/path';

const state = {};

const getters = {};

const mutations = {};

const actions = {
  // 获取所有标准评价模型列表
  async GetModelAllList({ commit }) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/GetAll@V1.0`,
      method: 'get'
    });
  },
  // 新增一条模型数据
  async InsertModel({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Insert@V1.0`,
      method: 'post',
      data
    });
  },
  // 编辑选择的模型数据
  async UpdateModel({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Update@V1.0`,
      method: 'put',
      data
    });
  },
  // 删除选择的模型
  async DelModel({ commit }, id) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/DeleteByID@V1.0?ID=${id}`,
      method: 'delete'
    });
  },
  // 通过选择的模型加载所有模型评价项
  async GetByModelID({ commit }, ModelID) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Item/GetByModelID@V1.0`,
      method: 'get',
      params: { ModelID }
    });
  },
  // 新增一条模型评价项
  async InsertModelItem({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Item/Insert@V1.0`,
      method: 'post',
      data
    });
  },
  // 编辑选择的模型评价项
  async UpdateModelItem({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Item/Update@V1.0`,
      method: 'put',
      data
    });
  },
  // 删除当前选择的模型评价项
  async SetDeterioration({ commit }, id) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Item/DeleteByID@V1.0?ID=${id}`,
      method: 'delete'
    });
  },
  // 获取监测标签列表
  async GetMonitorHaveList({ commit }) {
    return request({
      url: `${YXSZY_MONITOR_URL}/Point/Std/GetFlagHaveList@V1.0`,
      method: 'get'
    });
  },
  // 获取信号类型树
  async GetTreeList({ commit }) {
    return request({
      url: `${YXSZY_MONITOR_URL}/Signal/Type/Std/GetLogicalTreeList@V1.0`,
      method: 'get'
    });
  },
  // 获取重要程度选择列表
  async GetImportSelectList({ commit }) {
    return request({
      url: `${YXSZY_HEALTH}/Importance/Std/GetAll@V1.0`,
      method: 'get'
    });
  },
  // 获取劣化程度选择列表
  async GetDeterSelectList({ commit }) {
    return request({
      url: `${YXSZY_HEALTH}/Deterioration/Std/GetAll@V1.0`,
      method: 'get'
    });
  },
  // 通过泵站id 获取设备树列表
  async GetProductTreeListByStationID({ commit }, StationID) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Component/GetProductTreeListByStationID@V1.0`,
      method: 'get',
      params: { StationID }
    });
  },
  // 通过 设备id 获取部件列表
  async GetComponentListByProductID({ commit }, ProductID) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Component/GetComponentListByProductID@V1.0`,
      method: 'get',
      params: { ProductID }
    });
  },
  // 获取标准评价模型选择列表
  async GetSelectList({ commit }) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Model/Std/GetSelectList@V1.0`,
      method: 'get'
    });
  },
  // 绑定模型
  async GetBinding({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Component/Binding@V1.0`,
      method: 'post',
      data
    });
  },
  // 解绑模型
  async GetUnBinding({ commit }, data) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Component/UnBinding@V1.0`,
      method: 'post',
      data
    });
  },
  // 通过 ComponentID 获取绑定的模型
  async GetBindingModelByComponentID({ commit }, ComponentID) {
    return request({
      url: `${YXSZY_HEALTH}/Standard/Evaluation/Component/GetBindingModelByComponentID@V1.0`,
      method: 'get',
      params: { ComponentID }
    });
  },
  // 通过泵站id 获取设备树列表
  async GetProductTreeStationID({ commit }, StationID) {
    return request({
      url: `/YXSZY/Assets/Equipment/Std/GetTreeListByStationID@V1.0`,
      method: 'get',
      params: { StationID }
    });
  },
  // 通过设备id 获取最近评价记录
  async GetHistoryRecordList({ commit }, { EquipmentID, StartTime, EndTime }) {
    return request({
      url: `${YXSZY_HEALTH}/Multi/Evaluation/Record/GetRecordListByEquipmentIDOfTimeRange@V1.0`,
      method: 'get',
      params: { EquipmentID, StartTime, EndTime }
    });
  },
  // 获取评价树列表
  async GetEvaluateTreeRecordList({ commit }, { EvaluateTime, BindingID, ModelID, Severity }) {
    return request({
      url: `${YXSZY_HEALTH}/Multi/Evaluation/Record/GetEvaluateTreeRecord@V1.0`,
      method: 'get',
      params: { EvaluateTime, BindingID, ModelID, Severity }
    });
  },
  // 获取评价状态项列表
  async GetItemList({ commit }) {
    return request({
      url: `${YXSZY_HEALTH}/Result/Std/GetAll@V1.0`,
      method: 'get'
    });
  },
  // 通过设备id 获取最近评价记录
  async GetLastTreeRecordID({ commit }, EquipmentID) {
    return request({
      url: `${YXSZY_HEALTH}/Multi/Evaluation/Record/GetLastEvaluateTreeRecordByEquipmentID@V1.0`,
      method: 'get',
      params: { EquipmentID }
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
