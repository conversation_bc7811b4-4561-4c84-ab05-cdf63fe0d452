import request from '@/utils/request';
import storage from '@/utils/storage';

const actions = {
  // 上传文件
  async uploadFile({ rootState }, file) {
    let formData = new FormData();
    formData.append('file', file, file.name || 'file.svg');
    return request({
      url: '/sys-storage/upload',
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data',
        'Fawkes-Auth': storage.get('access_token')
      },
      data: formData
    });
  }
};
export default {
  namespaced: true,
  actions
};
