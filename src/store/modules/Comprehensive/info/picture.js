import request from '@/utils/request';
const damId = window.localStorage.getItem('comprehensivedamdata') ? JSON.parse(window.localStorage.getItem('comprehensivedamdata')).damId : null;

const state = () => ({
  pictureList: {
    content: [],
    totalElements: 120
  },
  PICTURE_LABEL: [
    'comprehensive.store.pictureLabel1', 'comprehensive.store.pictureLabel2', 'comprehensive.store.pictureLabel3', 'comprehensive.store.pictureLabel4', 'comprehensive.store.pictureLabel5', 'comprehensive.store.pictureLabel6', 'comprehensive.store.pictureLabel7', 'comprehensive.store.pictureLabel8'
  ],
  projectDrawing: [],
  alreadyPoint: []
});

const getters = {
};
const mutations = {
  SET_PICTURE_LIST(state, pictureList) {
    state.pictureList = pictureList;
  }
};
const actions = {
  // 工程图片列表查询
  async getPictureList({ rootState }, {label, name, page, size}) {
    let url = `${rootState.comprehensive.API}/dams/${this.state.comprehensive.infoDam.currentDamId || damId}/findPictureList?tagType=${label}&key=${name}`;
    if (page) {
      url += `&page=${page}`;
    }
    if (size) {
      url += `&size=${size}`;
    }
    return request({
      url: encodeURI(url),
      method: 'get'
    });
  },
  // 保存工程图片信息
  async savePicture({ rootState }, {file}) {
    return request({
      url: `${rootState.comprehensive.API}/dams/${this.state.comprehensive.infoDam.currentDamId || damId}/savePicture`,
      method: 'post',
      data: file
    });
  },
  // 删除工程图片
  async deletePictures({ rootState }, id) {
    return request({
      url: `${rootState.comprehensive.API}/dams/${this.state.comprehensive.infoDam.currentDamId || damId}/deleteProjectPictures?pictureIds=${id}`,
      method: 'delete'
    });
  },
  // 修改图片信息
  async getProjectPicture({ rootState }, imgid) {
    return request({
      url: `${rootState.comprehensive.API}/dams/${this.state.comprehensive.infoDam.currentDamId || damId}/findProjectPicture/${imgid}`,
      method: 'get'
    });
  }
};
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
