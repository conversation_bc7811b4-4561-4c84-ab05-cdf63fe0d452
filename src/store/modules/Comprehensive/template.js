import * as _ from 'lodash';
import request from '@/utils/request';
const damId = window.localStorage.getItem('comprehensivedamdata') ? JSON.parse(window.localStorage.getItem('comprehensivedamdata')).damId : null;

function initialState() {
  return {
    loading: false,
    processLoading: false,
    templatesData: {
      content: [],
      size: 10,
      pageable: {},
      totalElements: 0,
      totalPages: 0
    },
    currentPage: 1,
    TEMPLATE: {
      id: null,
      title: null,
      content: null,
      dateType: null,
      time: null,
      endDate: null,
      startDateType: 0,
      endDateType: 0,
      position: '',
      damId: '',
      pointIds: '',
      nodeId: '',
      tags: [],
      templateType: '',
      structure: {},
      thumbnail: null,
      valueCondition: '{"writeType":"0,1","auditStatus":"0,1","valueStatus":"0,1,2","valueType":"0,1,2"}'
    },
    copiedTemplate: {
      id: null,
      title: null,
      content: null,
      dateType: null,
      time: null,
      endDate: null,
      startDateType: 0,
      endDateType: 0,
      position: '',
      damId: '',
      pointIds: '',
      nodeId: '',
      tags: [],
      templateType: '',
      structure: {},
      thumbnail: null
    },
    onlyShowGraph: false,
    // 报告中修改模板
    templateMap: {},
    valueStatus: [],
    // 分布图监测类型
    examineTypes: [
      {name: 'comprehensive.store.examineType1', columnNumber: '571104100'},
      {name: 'comprehensive.store.examineType2', columnNumber: '571104101'}
    ],
    // 记忆上次选择的时间和类型
    record: {
      time: null,
      endDate: null,
      reportType: ''
    },
    // 过程线图的所有开始时间和结束时间
    processLineDateTimes: {},
    earliestAndLatestTime: {
      startTime: null,
      endTime: null
    },
    tags: [],
    // 模板列表缓存的筛选条件
    templateFilterForm: {},
    valueTime: {},
    /**
     * 优化的模板相关数据
     */
    showNewTeplateAside: false, // 是否展示新模板数据源
    operateDataSource: null, // 当前操作的数据源的option
    statisticMethodsList: [], // 统计方法列表
    dataAlgorithmList: [] // 取值算法列表
  };
}

const state = initialState;

const getters = {
  getComponentNameByTemplateType: () => (rawString) => {
    switch (rawString) {
      case 'MASTER':
        return 'TemplateMaster';
      case 'LAYOUT':
        return 'TemplateLayout';
      case 'PROCESS':
        return 'TemplateProcessLine';
      case 'CHART':
        return 'TemplateChart';
      case 'PROJECT':
        return 'TemplateBaseInfo';
      case 'DISTRIBUTION':
        return 'TemplateDistributionLine';
      case 'MONITORTABLE':
        return 'TemplateNewMaster';
      default:
        return '';
    }
  },
  // 分布图起始时间
  isStartTimeDisabled: () => (config) => {
    return config.examineType === '571104101';
  }
};

const mutations = {
  SET_STATISTIC_METHODS_LIST(state, statisticMethodsList) {
    state.statisticMethodsList = statisticMethodsList;
  },
  SET_DATA_ALGORITHM_LIST(state, dataAlgorithmList) {
    state.dataAlgorithmList = dataAlgorithmList;
  },
  // 是否展示数据源
  SET_SHOW_NEW_TEMPLATE_ASIDE(state, showNewTeplateAside) {
    state.showNewTeplateAside = showNewTeplateAside;
  },
  // 数据源
  SET_OPERATE_DATA_SOURCE(state, operateDataSource) {
    state.operateDataSource = operateDataSource;
  },
  SET_DATA_SOURCE(state, dataSource) {
    state.dataSource = dataSource;
  },
  SET_PROCESS_LOADING(state, processLoading) {
    state.processLoading = processLoading;
  },
  SET_TAGS(state, tags) {
    state.tags = tags;
  },
  SET_TEMPLATES_DATA(state, templatesData) {
    state.templatesData = templatesData;
  },
  SET_CURRENT_PAGE(state, currentPage) {
    state.currentPage = currentPage;
  },
  SET_COPIED_TEMPLATE(state, copiedTemplate) {
    state.copiedTemplate = copiedTemplate;
  },
  RESET_COPIED_TEMPLATE(state) {
    state.copiedTemplate = _.cloneDeep(state.TEMPLATE);
  },
  SET_ONLY_SHOW_GRAPH(state, onlyShowGraph) {
    state.onlyShowGraph = onlyShowGraph;
  },
  SET_TEMPLATE_MAP(state, templateMap) {
    state.templateMap = templateMap;
  },
  UPDATE_TEMPLATE_MAP(state, {key, value}) {
    if (state.templateMap[key]) {
      state.templateMap = {...state.templateMap, [key]: value};
    }
  },
  SET_VALUE_STATUS(state, valueStatus) {
    state.valueStatus = valueStatus;
  },
  SET_RECORD(state, record) {
    state.record = record;
  },
  SET_PROCESS_LINE_DATE_TIME(state, {index, dateTime}) {
    state.processLineDateTimes = {...state.processLineDateTimes, [index]: dateTime};
  },
  RESET_PROCESS_LINE_DATE_TIME(state) {
    state.processLineDateTimes = {};
  },
  SET_EARLIEST_AND_LATEST_TIME(state, {startTime, endTime}) {
    state.earliestAndLatestTime.startTime = startTime;
    state.earliestAndLatestTime.endTime = endTime;
  },
  SET_TEMPLATE_FILTER_FORM(state, templateFilterForm) {
    state.templateFilterForm = templateFilterForm;
  },
  RESET(state) {
    const init = initialState();
    Object.keys(init).forEach(key => {
      state[key] = init[key];
    });
  },
  SET_VALUE_TIME(state, time) {
    state.valueTime = time;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async getDamType({rootState}) {
   return request({
      url: `${rootState.comprehensive.API}/damJbxxs/damType`,
      method: 'get'
    }).then((res) => {
      return {
        ...res,
        data: res.filter(damInfo => nullCheck(damInfo, {name: 'name', value: 'nameInt'}))
      };
    });
  },
  async queryTemplates({commit, state, rootState}, {params, data, pageShare = false}) {
    if (pageShare) {
      params.page = state.currentPage - 1;
    }
    return request({
      url: `${rootState.comprehensive.API}/templates/query`,
      method: 'post',
      data,
      params
    }).then((res) => {
      commit('SET_TEMPLATES_DATA', res);
    });
  },
  async getTemplate({ rootState }, id) {
    return request({
      url: `${rootState.comprehensive.API}/templates/${id}`,
      method: 'get'
    });
  },
  // 分布图获取始测时间和截止时间
  async getExamineStartTimeAndEndTime({rootState}, params) {
    Object.keys(params).forEach((key) => {
      if (!params[key]) delete params[key];
    });
    return request({
      url: `${rootState.comprehensive.API}/findCompTime`,
      method: 'get',
      params
    });
  },
  // 分布图获取测值
  async getExamineValues({rootState}, params) {
    Object.keys(params).forEach((key) => {
      if (!params[key]) delete params[key];
    });
    return request({
      url: `${rootState.comprehensive.PROCESS}/codeinfo/dams/${params.damId}/findCompValue`,
      method: 'get',
      params
    });
  },
  async getValueStatus({commit, state, rootState}) {
    if (!state.valueStatus.length) {
      return request({
        url: `${rootState.comprehensive.API}/baseCodes/22`,
        method: 'get'
      }).then((valueStatus) => {
        commit('SET_VALUE_STATUS', valueStatus);
      });
    }
  },
  async getProcessLineMaxPoint({rootState}) {
    return request({
      url: `${rootState.comprehensive.API}/process-line/max-point`,
      method: 'get'
    });
  },
  async updateProcessLineMaxPoint({ rootState }, value) {
    return request({
      url: `${rootState.comprehensive.API}/process-line/max-point/${value}`,
      method: 'put',
      data: value
    });
  },
  async getDistribution({rootState}) {
    return request({
      url: `${rootState.comprehensive.API}/templates/distribution`,
      method: 'get'
    });
  },
  async getPointEarliestAndLatestTime({ commit, rootState }, params) {
    if (params && params.length) {
      return request({
        url: `${rootState.comprehensive.API}/damExamine/processLineTime`,
        method: 'get',
        params: { params: params.map(obj => JSON.stringify(obj)).join(';') }
      }).then(res => {
        const [startDate, endDate] = res;
        commit('SET_EARLIEST_AND_LATEST_TIME', {
          startTime: startDate ? new Date(startDate) : null,
          endTime: endDate ? new Date(endDate) : null
        });
      })
    } else {
      commit('SET_EARLIEST_AND_LATEST_TIME', {startTime: null, endTime: null});
    }
  },
  async getCompanyDamTree({rootState}) {
    return request({
      url: `${rootState.comprehensive.API}/dams/companyDamTree`,
      method: 'get'
    });
  },
  async getDamEarliestAndLatestTime({ commit, rootState }, params) {
    if (params) {
      const [startDate, endDate] = await this.$axios.$get(`${rootState.comprehensive.API}/damExamine/damSafeTime?params=${params}`);
      commit('SET_EARLIEST_AND_LATEST_TIME', {
        startTime: startDate ? new Date(startDate) : null,
        endTime: endDate ? new Date(endDate) : null
      });
    } else {
      commit('SET_EARLIEST_AND_LATEST_TIME', {startTime: null, endTime: null});
    }
  },
  async getStatisticMethods({rootState, commit}) {
    return request({
      url: `${rootState.comprehensive.API}/monitorTable/statisticMethods`,
      method: 'get'
    }).then(res => {
      commit('SET_STATISTIC_METHODS_LIST', res || []);
    });
  },
  async getDataAlgorithm({rootState, commit}) {
    return request({
      url: `${rootState.comprehensive.API}/monitorTable/dataAlgorithm`,
      method: 'get'
    }).then(res => {
      commit('SET_DATA_ALGORITHM_LIST', res || []);
    });
  }
};

// action helper
/**
 * @description 对当前对象进行nullCheck,如果为null 则返回false
 * @param targetObj 目标对象
 * @param defaultProps 对象监测的属性名称，默认为 name, value
 * @return Boolean
 */
function nullCheck(targetObj = {}, defaultProps = {name: 'name', value: 'value'}) {
  return targetObj[defaultProps.name] !== 'null' && targetObj[defaultProps.value] !== 'null' && !!targetObj[defaultProps.name] && !!targetObj[defaultProps.value];
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
