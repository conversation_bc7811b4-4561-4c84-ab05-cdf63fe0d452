import request from '@/utils/request';
import { ATTENDANCE } from '@/plugins/constants/path';

const state = {
  leaveTypeList: []
};

const getters = {};

const mutations = {
  // 请假类型
  SET_LEAVE_TYPE(state, data) {
    state.leaveTypeList = data;
  }
};

const actions = {
  // 请假记录列表
  async getLeaveList({ commit }, params) {
    return request({
      url: `${ATTENDANCE}/attendanceLeave/page`,
      method: 'get',
      params
    });
  },
  // 请假记录废弃
  async getLeaveDelete({ commit }, params) {
    return request({
      url: `${ATTENDANCE}/attendanceLeave/abandon`,
      method: 'delete',
      params
    });
  },
  // 假期类型列表
  async getHolidayList({ commit }) {
    return request({
      url: `${ATTENDANCE}/attendanceHoliday/list`,
      method: 'get'
    }).then(res => {
      commit('SET_LEAVE_TYPE', res.data || []);
    }).catch(() => {
      commit('SET_LEAVE_TYPE', []);
    });
  },
  // 用户假期余额
  async getLeaveBalance({ commit }, params) {
    return request({
      url: `${ATTENDANCE}/attendanceLeave/user_leave_balance`,
      method: 'get',
      params
    });
  },
  // 请假-验证是否能请假
  async isLeave({ commit }, params) {
    return request({
      url: `${ATTENDANCE}/attendanceLeave/verify_is_leave`,
      method: 'get',
      params
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
