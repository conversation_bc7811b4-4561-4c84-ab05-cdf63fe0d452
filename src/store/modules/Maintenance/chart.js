import request from '@/utils/request';
import dayjs from 'dayjs';
const state = {
  currentStation: null,
  currentGroup: '5',
  currentDate: '',
  currentPage: 'pump', // 当前显示页面
  dicts: null,
  radio: 100, // 默认选择日
  isChanged: false, // 是否修改过
  modifyTable: {}, // 记录更改过的表格数据
  isEmpty: false,
  // 以下是新运行报表的状态
  keys: {
    type: '',
    data: []
  },
  pointList: [],
  pointMap: {},
  groupList: [],
  groupIds: '',
  groupId: '',
  projectName: '',
  tableType: '', // 报表类型
  tableName: '', // 报表名称
  timeRange: '',
  viewType: 'TableView',
  currentProject: '',
  tableLoading: false,
  chartLoading: false
};
export const spliter = '@';
const getters = {
  tableName(state) {
    return state.projectName + state.tableType;
  },
  dateStr(state) {
    return dayjs(state.currentDate).format('YYYY-MM-DD 23:59:59');
  },
  paramsStr(state, getters) {
    return [
      getters.dateStr,
      state.timeRange,
      state.viewType === 'TableView' ? state.groupIds : state.groupId,
      state.keys.data.toString()
    ].join(spliter);
  },
  timeRangeOptions(state) {
    if (state.dict) {
      return state.dict.PointValueTimeRangeEnum;
    }
    return [];
  },
  showView(state, getters) {
    return getters.paramsStr.split(spliter).every(item => !!item);
  },
  pointStatementId(state) {
    if (state.dicts && state.tableName) {
      const result = state.dicts.StatementTableEnum.find(item => item.value === state.tableName);
      return result ? result.key : '100';
    }
  },
  currentPointList(state) {
    return state.pointList.filter(item => item.tableIds === state.tableType);
  }
};
const mutations = {
  SET_POINT_MAP(state, data) {
    state.pointMap = Object.assign(state.pointMap, data);
  },
  SET_CHART_LOADING(state, data) {
    state.chartLoading = data;
  },
  SET_TABLE_LOADING(state, data) {
    state.tableLoading = data;
  },
  SET_CURRENT_PROJECT(state, data) {
    state.currentProject = data;
  },
  SET_VIEW_TYPE(state, data) {
    state.viewType = data;
  },
  SET_TIME_RANGE(state, data) {
    state.timeRange = data;
  },
  SET_PROJECT_NAME(state, data) {
    state.projectName = data;
  },
  SET_TABLE_TYPE(state, data) {
    state.tableType = data;
  },
  SET_TABLE_NAME(state, data) {
    state.tableName = data;
  },
  SET_CURRENT_STATION(state, data) {
    state.currentStation = data;
  },
  SET_CURRENT_GROUP(state, data) {
    state.currentGroup = data;
  },
  SET_CURRENT_DATE(state, data) {
    state.currentDate = data;
  },
  SET_CHANGE_VALUE(state, data) {
    state.isChanged = data;
  },
  SET_DICT(state, data) {
    state.dicts = data;
  },
  SET_RADIO(state, data) {
    state.radio = data;
  },
  SET_MODIFY_TABLE(state, data) {
    if (data) {
      const {key, date, value, page, dateStr} = data;
      if (!state.modifyTable[page]) {
        state.modifyTable[page] = {};
      }
      if (!state.modifyTable[page][key]) {
        state.modifyTable[page][key] = [];
      }
      // 检验当前日期数据是否重复，需要去重
      const i = state.modifyTable[page][key].findIndex(el => el.date === date);
      if (i > -1) {
        // 覆盖旧数据
        state.modifyTable[page][key][i] = {date, value, dateStr};
      } else {
        state.modifyTable[page][key].push({date, value, dateStr});
      }
    } else {
      state.modifyTable = {};
    }
  },
  SET_CURRENT_PAGE(state, data) {
    state.currentPage = data;
  },
  SET_EMPTY(state, data) {
    state.isEmpty = data;
  },
  // 以下是新运行报表
  SET_KEYS(state, data) {
    state.keys = data;
  },
  SET_POINT_LIST(state, data) {
    if (Array.isArray(data)) {
      state.pointList.push(...data);
    } else {
      state.pointList = [];
    }
  },
  SET_GROUP_LIST(state, data) {
    state.groupList = data;
  },
  SET_GROUP_IDS(state, data) {
    state.groupIds = data;
  },
  SET_GROUP_ID(state, data) {
    state.groupId = data;
  }
};
const actions = {
  // 获取字典
  async getDict({commit}) {
    const {status, data} = await request({
      url: '/yx-dispatch/dict/all'
    });
    if (status) {
      commit('SET_DICT', data);
    }
  },
  // 获取水量
  async getWaterQuantities({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/water',
      params
    });
  },
  // 获取机组震摆记录
  async getOscillation({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/oscillation',
      params
    });
  },
  // 获取机组温度记录
  async getTemperature({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/temperature',
      params
    });
  },
  // 获取实时数据
  async getCurrentPointData({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/realTimeData',
      params
    });
  },
  // 保存测值
  async saveValue({commit}, data) {
    return request({
      url: '/yx-dispatch/statement/point/value/save',
      method: 'post',
      data
    });
  },
  // 电气运行记录
  async getElectrical({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/electrical',
      params
    });
  },
  // 机组压力记录
  async getPressure({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/pressure',
      params
    });
  },
  // 机组压力记录
  async getStatementPressure({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/pressure',
      params
    });
  },
  // 电气运行记录
  async getStatementElectrical({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/electrical',
      params
    });
  },
  // 获取图表数据
  async getChartData({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/chartStats',
      params
    });
  },
  // 导出
  async getExport({ commit }, params) {
    let url = '';
    const {tableName, ...rest} = params;
    switch (tableName) {
      case '水量统计表' :
        url = '/yx-dispatch/export/waterStatistics';
        break;
      case '机组压力记录':
        url = '/yx-dispatch/export/pressure';
        break;
      case '电气运行记录' :
        url = '/yx-dispatch/export/electric';
        break;
      case '机组震摆记录' :
        url = '/yx-dispatch/export/oscillation';
        break;
      case '机组温度记录' :
        url = '/yx-dispatch/export/temperature';
        break;
    }
    Reflect.deleteProperty(params, 'type');
    return request({
      url,
      method: 'get',
      params: rest,
      responseType: 'blob',
      responseEncoding: 'utf8'
    });
  },
  // 以下是新运行报表
  // 获取干线列表
  async getAreas() {
    return request({
      url: '/yx-dispatch/statement/area'
    });
  },
  // 获取工程列表
  async getProjectList({commit}, area) {
    return request({
      url: '/yx-dispatch/statement/project/list',
      params: {area}
    });
  },
  // 获取对象列表
  async getObjectList({commit}, data) {
    return request({
      url: '/yx-dispatch/statement/object/list',
      params: {projectId: data}
    });
  },
  // 获取运行报表
  async getTables({commit}, params) {
    return request({
      url: '/yx-dispatch/statement/tables',
      params
    });
  },
  // 获取报表的全部测点
  async getPointsOfTable({commit}, tableIds) {
    return request({
      url: '/yx-dispatch/statement/tables/points',
      params: {tableIds}
    }).then(res => {
      if (res.status) {
        commit('SET_POINT_LIST', res.data.allPoints.map(item => ({...item, tableIds})));
        return res.data.pointInfoList;
      } else {
        commit('SET_POINT_LIST', null);
      }
    });
  },
  // 获取表头，行等其他信息
  async getTableInfos({commit}, {params, data}) {
    return request({
      url: '/yx-dispatch/statement/tables/info',
      method: 'post',
      params,
      data
    });
  },
  // 获取表格数据
  async getTableDatas({commit}, {params, data}) {
    return request({
      url: '/yx-dispatch/statement/tables/datas',
      method: 'post',
      params,
      data
    });
  },
  // 测值保存
  async savePointData({commit}, data) {
    return request({
      url: '/yx-dispatch/statement/V2/point/value/save',
      method: 'post',
      data
    });
  },
  // 图表查询
  async getMaintenanceChartData({commit}, {params, data}) {
    return request({
      url: '/yx-dispatch/statement/V2/chartStats',
      method: 'post',
      params,
      data
    });
  },
  // 获取实时数据
  async queryPointData({commit}, {params, data}) {
    // 实时数据获取的当天数据
    params.date = dayjs().format('YYYY-MM-DD 23:59:59');
    return request({
      url: '/yx-dispatch/statement/V2/realTimeData',
      method: 'post',
      params,
      data
    });
  },
  // 新版导出接口
  async exportTableData({commit}, {params, data}) {
    return request({
      url: '/yx-dispatch/statement/export',
      method: 'post',
      params,
      data,
      responseType: 'blob',
      responseEncoding: 'utf8'
    });
  }
};
export default {
  state,
  getters,
  mutations,
  actions,
  namespaced: true
};
