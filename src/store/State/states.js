/*
 * @Author: xie_sm
 * @Date: 2022-04-15 08:54:47
 * @LastEditors: ye_xf
 * @LastEditTime: 2022-08-05 15:22:30
 * @FilePath: \mobile-template\src\store\State\states.js
 * @Description:
 *
 */
import * as types from './stateTypes'
import storage from '@/utils/storage'
import defaultSettings from '@/config'
import { IS_APP_CREATED, WX_USER_FULL_NAME } from './stateTypes'
import {PROJECT_PORTAL} from "@utils/constants";

const { responseEncrypt, requestEncrypt, behaviorAnalysis } = defaultSettings

const state = {
  [types.QUICK_ACCESS_AREA]: [],
  [types.TEMP_USER]: {},
  [types.ENUM]: {},
  [types.CURRENT_ROW]: '',
  [types.REJECT_TASK_KEY]: '',
  [types.SCHEDULE_TABLE]: '',
  [types.STATUS_BAR_HEIGHT]: 0,
  [types.USER_INFO]: {},
  [types.IS_LOGIN]: false,
  [types.PERMISSION_QUEUE]: new Set(),
  [types.ROUTES]: [],
  [types.PERMISSION]: {},
  [types.INTERFACE_CONFIG]: {},
  [types.PORTALS]: [],
  [types.PORTAL]: storage.getObject('portal'),
  [types.SECOND_LEVEL_PORTAL]: PROJECT_PORTAL,
  [types.DATA_SAFE]: {
    isUrlAuth: false,
    requestEncrypt, // 请求加密
    responseEncrypt, // 响应加密
  },
  [types.VISITED_VIEWS]: [],
  [types.CACHED_VIEWS]: [],
  [types.LANG]: {},
  [types.ADVANCED_CONFIG]: { behaviorAnalysis },
  [types.APPLY_RESOURCE]: 3,
  [types.FEISHU_USER_INFO]: null,
  [types.OPEN_ID]: '',
  [types.PLATFORM]: 'mobile',
  firstLoad: true,
  [types.LOADED]: false,
  [types.COLLAPSE]: Boolean(storage.get(types.COLLAPSE) === 'true'),
  [types.IS_APP_CREATED]: false,
  [types.FILTERS]: {},
  [types.PROJECT_INFO]: {},
  [types.FEISHU_LINK]: null,
  [types.GLOBAL_TABLE_DATA]: null,
  [types.TABLE_LOADING]: false,
  [types.GLOBAL_QUERY_PARAMS]: {},
  [types.PROVINCES]: [],
  [types.IS_PROJECT_CLOSED]: false,
  [types.AUTH_PORTALS]: [],
  [types.DATA_BASE_PORTAL]: {companyPortalId: ''},
  [types.OLD_PROJECT_PORTAL]: null,
  [types.VD_PROJECT_INFO]: {},
  [types.FAVORITE_PORTALS]: [],
  [types.IS_PHONE_VERIFY]: false,
  [types.PORTAL_STATUS_TABLE]: {},
}

export default state
