module.exports = {
  title: '', // 应用名称
  AUTH_TOKEN: 'Basic ZmF3a2VzOmZhd2tlc19zZWNyZXQ=', // 应用 tokentoken:ak:sk base64
  CLIENT: 'fawkes',
  CLIENT_SECRET: 'fawkes_secret',
  prefix: 'cs_', // 缓存前缀
  custom_flow_prefix: 'fawkes_custom_flow_', // 自定义流程前缀
  GRANT_TYPE: {
    password: { code: 'password', label: '密码模式', open: true },
    authorization_code: { code: 'authorization_code', label: '密码授权模式', open: true },
    sso: { code: 'sso', label: '单点登录模式', open: true },
    sms_code: { code: 'sms_code', label: '短信模式', open: true },
    sms_captcha: { code: 'sms_captcha', label: '短信验证码', open: true },
    img_captcha: { code: 'img_captcha', label: '图片验证码', open: true },
    refresh_token: { code: 'refresh_token', label: '刷新token模式', open: true }
  },
  /**
   * @description: 实时消息socket连接配置
   */
  maxReconnect: 100, // 最大重连次数
  reConnectInterval: 1000, // 重连间隔。单位：毫秒
  /**
   * 通用配置
   */
  footerView: true,
  copyright: '',
  theme: '#2F54EB',
  logo: './static/img/logo-nav.png',
  websiteLogo: './favicon.ico',
  language: 'en',
  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: false,
  /**
   * @type {boolean} true | false
   * @description Whether need tagsView
   */
  tagsView: 0,

  /**
   * @type {boolean} true | false
   * @description Whether need stripe
   */
  stripe: false,
  /**
   * @type {boolean} true | false
   * @description Whether show the navigation on the top
   */
  topMenu: false,
  timezone: 'Canada/Pacific',
  dateFormat: '',
  /**
   * 开发配置
   * 本地修改不允许上传
   */

  // 代理设置,开发环境默认baseUrl为/api，如需代理至其它地址，请在默认代理前设置路径
  proxy: {
    // 正式服
    // '/api/YXSZY': {
    //   target: 'https://yxywweb.hdec.com',
    //   changeOrigin: true
    // },
    // '/api': {
    //   target: 'https://yxywweb.hdec.com',
    //   changeOrigin: true
    // },
    // 测试服
    '/api/YXSZY': {
      target: 'http://**************:8901',
      changeOrigin: true,
      pathRewrite: {
        '/api/YXSZY': '/YXSZY'
      }
    },
    '/api': {
      target: 'http://**************/api',
      changeOrigin: true,
      pathRewrite: {
        '/api': ''
      }
    },
    '/onlyoffice': {
      target: 'http://*************:5000',
      changeOrigin: true,
      pathRewrite: {
        '/onlyoffice': ''
      }
    },
    '/DataServer': {
      target: `http://t6.tianditu.gov.cn/DataServer`,
      changeOrigin: true,
      pathRewrite: {
        '/DataServer': ''
      }
    },
    '/styles': {
      target: `https://api.mapbox.com`,
      changeOrigin: true,
      pathRewrite: {
        '/styles': '/styles'
      }
    },
    '/iserver2': {
      // target: 'http://yxgis2sim.hdec.com',
      target: 'https://yxywweb.hdec.com',
      changeOrigin: true,
      pathRewrite: {
        '/iserver2': '/iserver'
      }
    },
    '/iserver': {
      // target: `http://**************`,
      // target: `http://gisct.hdec.com`,
      target: 'https://yxywweb.hdec.com',
      changeOrigin: true,
      pathRewrite: {
        '/iserver': '/iserver'
      }
    },
    '/yuxiBasemap': {
      // target: 'http://**************',
      // target: 'http://yxgis2sim.hdec.com',
      target: 'https://yxywweb.hdec.com',
      changeOrigin: true,
      pathRewrite: {
        '/yuxiBasemap': '/yuxiBasemap'
      }
    },
    '/sysperfix': {
      target: 'http://**************/',
      changeOrigin: true
    },
    '/yuxi_agent': {
      // target: 'http://yxgis2sim.hdec.com',
      // target: `http://************:80`,
      target: 'https://yxywweb.hdec.com',
      changeOrigin: true,
      pathRewrite: {
        '/yuxi_agent': '/yuxi_agent'
      }
    },
    '/portalproxy': {
      target: 'http://iportalnew.ecidi.com',
      changeOrigin: true
    }
  },
  /**
   * @type {boolean} true | false
   * @description 是否加载本地 路由|权限
   */
  localRoute: false,
  /**
   * @type {boolean} true | false
   * @description 是否开启国际化 语言配置 1.6 为 localLang
   */
  i18n: false,
  /**
   * @type {boolean} true | false
   * @description 是否加载本地 Svg组件 svg目录src/assets/iconfont/svg
   */
  localSvg: true,
  /**
   * @type {boolean} true | false
   * @description 是否开启多门户
   */
  multiPortal: true,
  /**
   * @type {boolean} true | false
   * @description 是否强制密码修改
   */
  password: true,
  /**
   * @type {Number}
   * @description 校验服务器时间戳间隔
   */
  tsDvalue: 60000
};
