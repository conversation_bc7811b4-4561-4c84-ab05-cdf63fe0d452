<template>
	<view :class="className">
		<template v-if="status">
      <template v-if="list && list.length !== 0">
        <template v-if="card">
          <u-card
            :border="false"
            :foot-border-top="false"
            :padding="0"
            box-shadow="0px 2px 16px 0px rgba(104,115,127,0.06)"
            :border-radius="16"
            :head-border-bottom="false"
            :show-foot="false"
            v-for="(item, index) in list"
            :key="(keyName ? item[keyName] + item.id + index: item.id || item.taskId) + new Date().getTime()"
            @click="isListBody || isTap ? $emit('click', item) : ''"
          >
           <view slot="head">
              <slot v-if="isHead" name="cardHead" :row="item" :index="index"></slot>
              <view v-else class="u-font-30 text-weight-500 text-black1 ws-pre-wrap word-break-all word-wrap" @click="isListBody || isTap ? '' : $emit('click', item)">
                {{ item[name] }}
              </view>
            </view>
            <view class="u-p-l-30 u-p-r-30" slot="body">
              <slot v-if="isListBody" name="listBody"  :row="item" :index="index"></slot>
              <slot v-else-if="isTitleOther" name="titleOther" :row="item"></slot>
              <view v-if="!isListBody" class="u-body-item u-flex u-col-between u-p-t-0 u-row-between" :class="{'u-p-b-30': !isFoot }">
                <view class="u-flex u-col-center u-font-26 text-gray-dark3"  @click="isListBody || isTap ? '' : $emit('click', item)">
                  <u-image :src="getTaskImg('day')" width="28" height="28"  class="u-m-r-16"></u-image>
                  <text class="u-font-28 text-weight-normal">{{ timeField ? item[timeField] : getTime(item) }}</text>
                </view>
                <slot name="listOther" :row="item" :index="index"></slot>
                <slot name="listTimeRight"  :row="item" :index="index"></slot>
              </view>
              <view v-if="isFoot" class="u-p-b-30">
                <slot name="cardFoot" :row="item" :index="index"></slot>
              </view>
            </view>
          </u-card>
        </template>
        <slot v-else name="list"></slot>
      </template>
      <empty-data v-if="status === 'nomore' && list.length === 0" :is-full="isFull"></empty-data>
      <u-loadmore v-else class="u-p-t-30 u-p-b-30" :status="status" />
    </template>
    <template v-else>
      <view v-if="loading" class="minh-20 u-flex u-col-center u-row-center u-text-center">
        <u-loading show  mode="flower" size="60"></u-loading>
      </view>
      <template v-else-if="list && list.length !== 0">
        <slot name="list"></slot>
      </template>
      <!-- <u-empty v-else text="暂无数据" class="bg-white u-p-30" :mode="mode"></u-empty> -->
      <empty-data v-else :is-full="isFull"></empty-data>
    </template>
	</view>
</template>

<script>
  import EmptyData from '@/components/emptyData/emptyData.vue';
	export default {
		name:"cellList",
    props: {
      keyName: {
        type: String,
        default: ''
      },
      isFull: {
        type: Boolean,
        default: false
      },
      loading: {
        type: Boolean,
        default: false
      },
      list: {
        type: Array,
        default() {
          return []
        }
      },
      status: {
        type: String,
        default: ''
      },
      mode: {
        type: String,
        default: 'data'
      },
      name: {
        type: String,
        default: 'name'
      },
      card: {
        type: Boolean,
        default: false
      },
      timeField: {
        type: String,
        default: ''
      },
      getTime: {
        type: Function,
        default() {
          return () => {};
        }
      },
      isListBody: {
        type: Boolean,
        default: false
      },
      isTap: {
        type: Boolean,
        default: false
      },
      isTitleOther: {
        type: Boolean,
        default: false
      },
      isFoot: {
        type: Boolean,
        default: false
      },
      isHead: {
        type: Boolean,
        default: false
      }
    },
    components: {
      EmptyData
    },
    computed: {
      className() {
        const flag = this.status ? this.status === 'nomore' && this.list.length === 0 : this.loading ? false : this.list && this.list.length !== 0
        return flag ? 'bg-white' : '';
      }
    },
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss">

</style>
