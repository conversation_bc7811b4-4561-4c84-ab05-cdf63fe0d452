<template>
  <u-popup mode="center" v-model="show" :border-radius="20">
  	<view class="app-popup">
  		<view style="padding-right:4rem;">
  			<text class="u-tips-color">请选择你要提交的申请单</text>
  		</view>
  		<view>
  			<text class="app-bk" @click="getPermissionInfo({ key: 'applyReissueCard' })">补卡</text>
  			<text class="app-bk" @click="getPermissionInfo({ key: 'applyLeave' })">请假申请</text>
  			<text class="app-bk" @click="getPermissionInfo({ key: 'applyBusinessTrip' })">出差申请</text>
  		</view>
  		<view class="confrim-btn">
  			<text @click="show = false;" style="color: #2979ff;">取消</text>
  		</view>
  	</view>
  </u-popup>
</template>

<script>
  import { mapMutations } from 'vuex';
  export default {
    name: 'abnormal',
    data() {
      return {
        show: false,
        currItem: {}
      }
    },
    methods: {
      ...mapMutations('punchClock', ['SET_ABNORMAL']),
      handleDispose(item) {
      	this.show = true;
        this.currItem = item;
        console.log(item)
      },
      getPermissionInfo(val) {
        // this.SET_ABNORMAL(val.name ? {} : this.currItem);
        this.SET_ABNORMAL(this.currItem);
        this.show = false;
        uni.navigateTo({
          url: val.key === 'applyLeave' ? '/pages/applyLeave/applyLeave' : `/pages/formCenter/formCenter?type=add&formKey=${val.key}`
        });
      },
    }
  }
</script>

<style lang="scss" scoped>
  .app-popup {
  	padding: 30rpx 35rpx;
  }
  .app-bk {
  	display: block;
  	margin-top: 40rpx;
  }
  .confrim-btn {
  	float: right;
  	margin: 2.3rem 0 25rpx;
  }
</style>
