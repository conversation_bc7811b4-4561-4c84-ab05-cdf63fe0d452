<template>
  <view class="submitView">
    <u-button :disable="submitLoading" :loading="submitLoading" type="primary" @click="submit">提交</u-button>
  </view>
</template>

<script>
  export default {
    name: "submit-view",
    data() {
      return {

      };
    },
    props: {
      submitLoading: {
        type: Boolean,
        required: true
      }
    },
    methods: {
      submit() {
        this.$emit('submit');
      }
    }
  }
</script>

<style lang="scss">
  .submitView {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    height: 102rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    z-index: 66;
    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.14);
    .u-btn {
      width: 100vw;
    }
  }
</style>
